import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './components/ui/card';
import { Button } from './components/ui/button';
import { Input } from './components/ui/input';
import { Textarea } from './components/ui/textarea';

const EmailVerifier = () => {
  const [apiConfig, setApiConfig] = useState({
    uid: '',
    sign: ''
  });

  const [emails, setEmails] = useState('');
  const [settings, setSettings] = useState({
    titleFilter: '验证码',
    timeRange: '10',  // 扩大到10分钟，防止新邮件导致旧验证码丢失
    extractRule: '$BODYTEXT-R|\\d{6}$'
  });

  const [emailResults, setEmailResults] = useState({});
  const [logs, setLogs] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isConfigLoaded, setIsConfigLoaded] = useState(false); // 配置加载状态锁
  
  // 🔍 完整邮件内容模态框状态
  const [emailDetailModal, setEmailDetailModal] = useState({
    isOpen: false,
    email: '',
    content: ''
  });

  // 配置版本常量
  const CONFIG_VERSION = '3.0.0';
  const CONFIG_KEY = 'emailVerifierConfig';

  // 日志自动滚动引用
  const logEndRef = useRef(null);

  // 配置保存函数 - 实时同步，状态锁，完整性检查
  const saveConfig = () => {
    if (!isConfigLoaded) {
      console.log('⏳ 配置尚未加载完成，跳过保存操作');
      return;
    }

    try {
      const emailList = emails.split('\n').filter(email => email.trim());
      
      // 🔧 关键修复：防止双重转义
      const configToSave = {
        version: CONFIG_VERSION,
        uid: apiConfig.uid,
        sign: apiConfig.sign,
        emails: emailList,
        titleFilter: settings.titleFilter,
        timeRange: settings.timeRange,
        extractRule: settings.extractRule, // 直接保存，不进行额外转义
        timestamp: new Date().toISOString(),
        saveCounter: (JSON.parse(localStorage.getItem(CONFIG_KEY) || '{}').saveCounter || 0) + 1
      };

      // 🔍 调试：检查保存前的规则格式
      console.log('💾 保存配置前的规则检查:', {
        原始规则: settings.extractRule,
        规则长度: settings.extractRule ? settings.extractRule.length : 0,
        包含反斜杠数量: (settings.extractRule.match(/\\/g) || []).length,
        JSON序列化后: JSON.stringify({ extractRule: settings.extractRule }),
        即将保存的规则: configToSave.extractRule
      });

      // 🔍 验证API配置
      console.log('💾 保存前API配置检查:', {
        uid: configToSave.uid,
        sign: configToSave.sign ? '已设置' : '未设置',
        uid类型: typeof configToSave.uid,
        sign类型: typeof configToSave.sign
      });

      const jsonString = JSON.stringify(configToSave);
      localStorage.setItem(CONFIG_KEY, jsonString);
      
      // 🔍 验证保存后的数据
      const savedData = JSON.parse(localStorage.getItem(CONFIG_KEY));
      console.log('✅ 保存后验证规则格式:', {
        保存的规则: savedData.extractRule,
        反斜杠数量: (savedData.extractRule.match(/\\/g) || []).length,
        与原始规则相同: savedData.extractRule === settings.extractRule,
        uid是否保存: !!savedData.uid,
        sign是否保存: !!savedData.sign
      });

      console.log('配置已立即保存 (实时同步+状态锁+完整性检查):', {
        version: configToSave.version,
        uid前几位: configToSave.uid ? configToSave.uid.substring(0, 3) + '...' : '未设置',
        sign是否存在: !!configToSave.sign,
        extractRule: configToSave.extractRule,
        timestamp: configToSave.timestamp,
        saveCounter: configToSave.saveCounter,
        configSize: jsonString.length,
        emailCount: configToSave.emails.length
      });
      
      // 添加成功日志
      addLog(`✅ 配置已成功保存 (${configToSave.emails.length}个邮箱)`);
      
      // 显示API配置状态
      if (configToSave.uid && configToSave.sign) {
        addLog(`✅ API配置已保存: ID=${configToSave.uid.substring(0, 3)}...`);
      } else {
        addLog('⚠️ API配置不完整，请设置授权ID和API密钥');
      }
      
    } catch (error) {
      console.error('❌ 配置保存失败:', error);
      addLog(`❌ 配置保存失败: ${error.message}`);
    }
  };

  // 配置加载函数 - 防止双重转义
  const loadConfig = () => {
    try {
      const savedConfig = localStorage.getItem(CONFIG_KEY);
      if (savedConfig) {
        const config = JSON.parse(savedConfig);
        
        // 🔍 调试：检查加载的规则格式和API配置
        console.log('📂 加载配置时的检查:', {
          加载的规则: config.extractRule,
          规则长度: config.extractRule ? config.extractRule.length : 0,
          反斜杠数量: config.extractRule ? (config.extractRule.match(/\\/g) || []).length : 0,
          规则类型: typeof config.extractRule,
          是否有UID: !!config.uid,
          是否有SIGN: !!config.sign,
          UID类型: typeof config.uid,
          SIGN类型: typeof config.sign,
          配置版本: config.version
        });
        
        // 🔧 关键修复：修复双重转义问题
        let correctedRule = config.extractRule;
        if (correctedRule && typeof correctedRule === 'string') {
          // 🔧 更精确的双重转义检测和修复
          if (correctedRule.includes('\\\\d')) {
            // 如果发现双重转义的模式（\\\\d），修复为标准格式（\\d）
            const originalRule = correctedRule;
            correctedRule = correctedRule
              .replace(/\\\\d/g, '\\d')   // \\\\d → \\d
              .replace(/\\\\b/g, '\\b')   // \\\\b → \\b  
              .replace(/\\\\s/g, '\\s')   // \\\\s → \\s
              .replace(/\\\\w/g, '\\w');  // \\\\w → \\w
            
            console.log('🔧 检测到双重转义，已修复:', {
              原始规则: originalRule,
              修复后规则: correctedRule,
              修复前反斜杠数量: (originalRule.match(/\\/g) || []).length,
              修复后反斜杠数量: (correctedRule.match(/\\/g) || []).length,
              修复了: originalRule !== correctedRule
            });
          }
          
          // 🔧 额外检查：确保规则格式正确
          if (!correctedRule.startsWith('$') || !correctedRule.includes('|')) {
            console.warn('⚠️ 检测到异常规则格式，使用默认规则:', {
              异常规则: correctedRule,
              默认规则: '$BODYTEXT-R|\\d{6}$'
            });
            correctedRule = '$BODYTEXT-R|\\d{6}$';
          }
        }

        // 版本升级处理
        if (config.version !== CONFIG_VERSION) {
          console.log('🔄 配置版本升级:', {
            当前版本: config.version,
            目标版本: CONFIG_VERSION,
            是否保留用户数据: true
          });
        }
        
        // 设置配置，使用修复后的规则
        if (config.uid && config.sign) {
          console.log('✅ 成功加载API配置:', {
            uid前几位: config.uid.substring(0, 3) + '...',
            sign是否存在: !!config.sign
          });
          setApiConfig({ uid: config.uid, sign: config.sign });
        } else {
          console.warn('⚠️ 未找到API配置或格式错误:', {
            uid: config.uid,
            sign: config.sign ? '已设置' : '未设置'
          });
        }
        
        if (config.emails && Array.isArray(config.emails)) {
          setEmails(config.emails.join('\n'));
        }
        
        setSettings({
          titleFilter: config.titleFilter || '',
          timeRange: config.timeRange || '10',
          extractRule: correctedRule || '$BODYTEXT-R|\\d{6}$' // 使用修复后的规则
        });

        console.log('✅ 配置加载完成:', {
          version: config.version,
          hasApiConfig: !!(config.uid && config.sign),
          emailCount: config.emails ? config.emails.length : 0,
          extractRule: correctedRule,
          修复了双重转义: correctedRule !== config.extractRule
        });
        
        return true;
      } else {
        console.log('📝 首次使用，使用默认配置');
        setSettings({
          titleFilter: '',
          timeRange: '10',
          extractRule: '$BODYTEXT-R|\\d{6}$'
        });
        return false;
      }
      
      setIsConfigLoaded(true);
    } catch (error) {
      console.error('❌ 配置加载失败:', error);
      setIsConfigLoaded(true);
      return false;
    }
  };

  // 🔧 组件加载时自动读取配置 - 增强错误恢复和调试信息
  useEffect(() => {
    if (!isConfigLoaded) {
      console.log('🔧 开始配置加载流程...', {
        currentVersion: CONFIG_VERSION,
        timestamp: new Date().toISOString(),
        isConfigLoaded
      });
      
      try {
        const configLoaded = loadConfig();
        setIsConfigLoaded(true); // 设置配置已加载标志
        
        if (!configLoaded) {
          addLog('🆕 首次使用，请配置API信息');
          console.log('📋 使用默认配置，配置状态:', {
            apiConfig: { uid: '', sign: '' },
            emails: '',
            settings: {
              titleFilter: '验证码',
              timeRange: '10',
              extractRule: '$BODYTEXT-R|\\d{6}$'
            }
          });
        } else {
          // 配置加载成功，验证完整性
          setTimeout(() => {
            console.log('✅ 配置加载完成，当前状态验证:', {
              version: CONFIG_VERSION,
              hasApiConfig: !!(apiConfig.uid && apiConfig.sign),
              uid前几位: apiConfig.uid ? apiConfig.uid.substring(0, 3) + '...' : '未设置',
              sign是否存在: !!apiConfig.sign,
              emailCount: emails.split('\n').filter(e => e.trim()).length,
              extractRule: settings.extractRule,
              configIntegrity: '正常'
            });
            
            // 显示API配置状态到日志
            if (apiConfig.uid && apiConfig.sign) {
              addLog(`✅ 已加载API配置: ID=${apiConfig.uid.substring(0, 3)}...`);
            } else {
              addLog('⚠️ API配置不完整，请设置授权ID和API密钥');
            }
          }, 100);
        }
      } catch (error) {
        console.error('❌ 配置加载流程异常:', error);
        addLog(`❌ 配置初始化失败: ${error.message}`);
        
        // 强制使用默认配置并清理损坏数据
        try {
          localStorage.clear();
          setApiConfig({ uid: '', sign: '' });
          setEmails('');
          setSettings({
            titleFilter: '',
            timeRange: '10',  // 扩大到10分钟，防止新邮件导致旧验证码丢失
            extractRule: '$BODYTEXT-R|\\d{6}$'
          });
          setIsConfigLoaded(true);
          addLog('🔧 已强制重置为默认配置');
        } catch (resetError) {
          console.error('🚨 强制重置失败:', resetError);
          addLog(`🚨 严重错误: 无法重置配置 - ${resetError.message}`);
        }
      }
    }
  }, [isConfigLoaded, apiConfig.uid, apiConfig.sign, emails, settings.extractRule]);

  // 🔧 当配置改变时自动保存 - 立即同步保存，消除竞态条件
  useEffect(() => {
    // 只在配置已加载且有实际配置内容时才保存
    if (isConfigLoaded && (apiConfig.uid || apiConfig.sign || emails)) {
        try {
          // 🔧 修复：使用与saveConfig相同的格式，确保一致性
          const emailList = emails.split('\n').filter(email => email.trim());
          
          const configToSave = {
            version: CONFIG_VERSION,
            uid: apiConfig.uid,
            sign: apiConfig.sign,
            emails: emailList,
            titleFilter: settings.titleFilter,
            timeRange: settings.timeRange,
            extractRule: settings.extractRule,
            timestamp: new Date().toISOString(),
            saveCounter: (JSON.parse(localStorage.getItem(CONFIG_KEY) || '{}').saveCounter || 0) + 1
          };
        
          // 🔍 配置数据完整性检查
          const isConfigValid = (
            typeof configToSave.version === 'string' &&
            typeof configToSave.uid === 'string' &&
            typeof configToSave.sign === 'string' &&
            Array.isArray(configToSave.emails) &&
            typeof configToSave.titleFilter === 'string' &&
            typeof configToSave.timeRange === 'string' &&
            typeof configToSave.extractRule === 'string' &&
            configToSave.extractRule.length > 0
          );
        
          if (!isConfigValid) {
            console.warn('⚠️ 配置数据格式异常，跳过保存:', configToSave);
            addLog('⚠️ 配置数据验证失败，已跳过保存');
            return;
          }
        
          const jsonString = JSON.stringify(configToSave);
          localStorage.setItem(CONFIG_KEY, jsonString);
        
          // 🔧 增强调试日志便于排查问题
          console.log('配置已立即保存 (自动保存):', { 
            version: configToSave.version,
            uid: configToSave.uid.substring(0, 3) + '...',
            sign: configToSave.sign ? '已设置' : '未设置',
            extractRule: configToSave.extractRule,
            timestamp: configToSave.timestamp,
            saveCounter: configToSave.saveCounter,
            configSize: jsonString.length,
            emailCount: configToSave.emails.length
          });
        
        } catch (error) {
          console.error('保存配置失败:', error);
          addLog(`❌ 配置保存失败: ${error.message}`);
        
          // 🔧 保存失败时的错误恢复机制
          try {
            // 尝试读取当前localStorage状态
            const currentSaved = localStorage.getItem(CONFIG_KEY);
            if (currentSaved) {
              const currentConfig = JSON.parse(currentSaved);
              console.log('🔧 保存失败，但检测到现有有效配置:', {
                savedVersion: currentConfig.version,
                savedTimestamp: currentConfig.timestamp
              });
              addLog('🔧 保存失败，但现有配置完整');
            } else {
              addLog('⚠️ 保存失败且无现有配置，请手动保存');
            }
          } catch (recoveryError) {
            console.error('配置恢复检查失败:', recoveryError);
            addLog('🚨 配置系统异常，建议重置应用');
          }
        }
    }
  }, [apiConfig, emails, settings, isConfigLoaded]);

  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  // 防止启动闪烁 - 组件加载完成后显示页面
  useEffect(() => {
    // 确保DOM完全渲染后再显示
    const timer = setTimeout(() => {
      document.body.classList.add('loaded');
    }, 100);
      
      return () => clearTimeout(timer);
  }, []);

  // 🔧 模态框ESC键关闭功能
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && emailDetailModal.isOpen) {
        setEmailDetailModal({ isOpen: false, email: '', content: '' });
      }
    };

    if (emailDetailModal.isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // 阻止背景滚动
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [emailDetailModal.isOpen]);



  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  // 按官方文档标准重新设计提取规则函数
  const applyExtractionRule = (emailContent, rule) => {
    if (!emailContent || !rule) return emailContent;
    
    try {
      console.log('🎯 开始应用提取规则:', {
        规则: rule,
        邮件内容长度: emailContent.length,
        邮件预览: emailContent.substring(0, 100) + '...'
      });
      
      // 完整内容
      if (rule === '$BODYTEXT$') {
        console.log('📄 使用完整邮件内容规则');
        return emailContent;
      }
      
      // 解析官方标准格式：$BODYTEXT-R|正则表达式$
      if (rule.startsWith('$BODYTEXT-R|') && rule.endsWith('$')) {
        const regexStr = rule.slice(12, -1); // 提取中间的正则表达式
        console.log('🔍 正则提取调试信息:', {
          originalRule: rule,
          extractedRegexStr: regexStr,
          regexStrLength: regexStr.length,
          regexStrChars: regexStr.split('').map((char, index) => `${index}: '${char}' (${char.charCodeAt(0)})`),
          emailContentPreview: emailContent.substring(0, 100)
        });
        
        // 创建正则表达式并执行匹配
        const regex = new RegExp(regexStr);
        console.log('📝 创建的正则表达式对象:', {
          regexSource: regex.source,
          regexFlags: regex.flags,
          regexString: regex.toString()
        });
        
        const match = emailContent.match(regex);
        
        if (match) {
          console.log('✅ 匹配成功:', {
            fullMatch: match[0],
            allMatches: match,
            matchIndex: match.index
          });
          return match[0];
        } else {
          console.log('❌ 未匹配到内容:', {
            regexUsed: regex.toString(),
            emailSample: emailContent.substring(0, 200),
            // 测试一些常见的验证码模式
            testDigits6: /\d{6}/.test(emailContent),
            testDigits5: /\d{5}/.test(emailContent),
            testDigits4: /\d{4}/.test(emailContent),
            testDigits4to8: /\d{4,8}/.test(emailContent),
            // 提取所有数字序列用于分析
            allDigitSequences: emailContent.match(/\d+/g)
          });
          
          // 🔧 智能建议：分析邮件内容并建议合适的提取规则
          const digitSequences = emailContent.match(/\d+/g);
          if (digitSequences && digitSequences.length > 0) {
            const suggestions = [];
            digitSequences.forEach(seq => {
              const length = seq.length;
              if (length === 4) suggestions.push('4位数字验证码');
              if (length === 5) suggestions.push('4-8位数字或4个及以上数字');
              if (length === 6) suggestions.push('6位数字验证码');
              if (length >= 4 && length <= 8) suggestions.push('4-8位数字');
            });
            
            console.log('💡 智能建议:', {
              找到的数字序列: digitSequences,
              建议的提取规则: [...new Set(suggestions)], // 去重
              当前失败规则: rule,
              失败原因: `当前规则要求的格式与邮件中的数字不匹配`
            });
          }
          
          return '未匹配到内容';
        }
      }
      
      // 处理其他格式
      if (rule.startsWith('$TITLE-R|') && rule.endsWith('$')) {
        // 主题提取（这里简化处理，实际应该从邮件主题提取）
        const regexStr = rule.slice(9, -1);
        console.log('📋 处理邮件主题提取规则:', { regexStr });
        const regex = new RegExp(regexStr);
        const match = emailContent.match(regex);
        return match ? match[0] : '未匹配到内容';
      }
      
      if (rule.startsWith('$BODYCLEAR-R|') && rule.endsWith('$')) {
        // 清理版本提取
        const regexStr = rule.slice(13, -1);
        console.log('🧹 处理邮件清理版本提取规则:', { regexStr });
        const regex = new RegExp(regexStr);
        const match = emailContent.match(regex);
        return match ? match[0] : '未匹配到内容';
      }
      
      if (rule.startsWith('$BODY|') && rule.endsWith('$')) {
        // 链接提取等特殊规则
        console.log('🔗 处理特殊提取规则:', { rule });
        // 这里可以添加更多特殊规则处理
        return '未匹配到内容';
      }
      
      // 🔧 关键修复：不再返回完整邮件内容作为兜底
      console.warn('⚠️ 未识别的提取规则格式:', {
        rule,
        ruleType: typeof rule,
        supportedFormats: [
          '$BODYTEXT$',
          '$BODYTEXT-R|正则表达式$',
          '$TITLE-R|正则表达式$',
          '$BODYCLEAR-R|正则表达式$',
          '$BODY|特殊格式$'
        ]
      });
      return '未识别的规则格式';
    } catch (error) {
      console.error('❌ 提取规则应用失败:', {
        error: error.message,
        rule,
        emailLength: emailContent ? emailContent.length : 0
      });
      return `规则应用失败: ${error.message}`;
    }
  };

  // 提取规则切换处理函数 - 仅更新设置，不自动查询
  const handleExtractRuleChange = (newRule) => {
    // 🔧 关键修复：立即标准化输入规则，防止双重转义传播
    const normalizedNewRule = newRule && newRule.includes('\\\\d') ? 
      newRule
        .replace(/\\\\d/g, '\\d')   // \\\\d → \\d
        .replace(/\\\\b/g, '\\b')   // \\\\b → \\b  
        .replace(/\\\\s/g, '\\s')   // \\\\s → \\s
        .replace(/\\\\w/g, '\\w')   // \\\\w → \\w
        .replace(/\\\\W/g, '\\W')   // \\\\W → \\W
        .replace(/\\\\D/g, '\\D')   // \\\\D → \\D
        .replace(/\\\\S/g, '\\S')   // \\\\S → \\S
      : newRule;
      
    if (normalizedNewRule !== newRule) {
      console.log('🔧 输入规则标准化修复:', {
        输入规则: newRule,
        标准化规则: normalizedNewRule,
        修复前反斜杠数量: (newRule.match(/\\/g) || []).length,
        修复后反斜杠数量: (normalizedNewRule.match(/\\/g) || []).length
      });
    }
    
    console.log('🔄 切换提取规则:', {
      from: settings.extractRule,
      to: normalizedNewRule
    });
    
    // 仅更新设置，不执行任何查询操作
    setSettings(prev => ({
      ...prev,
      extractRule: normalizedNewRule
    }));
    
    // 记录切换日志
    const ruleDescription = getRuleDescription(normalizedNewRule);
    addLog(`🔄 提取规则已切换: ${ruleDescription}`);
    addLog(`💡 点击"开始查询"使用新规则获取验证码`);
    
    console.log('✅ 提取规则切换完成:', {
      newRule: normalizedNewRule,
      description: ruleDescription,
      nextAction: '等待用户点击开始查询'
    });
  };

  // 获取规则描述 - 使用更清晰的规则定义方式
  const getRuleDescription = (rule) => {
    // 🔧 规则构建辅助函数 - 避免双反斜杠困惑
    const buildBodyTextRule = (pattern) => `$BODYTEXT-R|${pattern}$`;
    const buildTitleRule = (pattern) => `$TITLE-R|${pattern}$`;
    const buildBodyClearRule = (pattern) => `$BODYCLEAR-R|${pattern}$`;
    
    // 🔧 关键修复：规则标准化处理，修复双重转义问题
    const normalizeRule = (inputRule) => {
      if (!inputRule || typeof inputRule !== 'string') return inputRule;
      
      // 🔧 修复：检测并修复双重转义问题
      // 在JavaScript字符串中：\\\\d 表示实际的 \\d 字符序列（4个反斜杠字符）
      // 我们需要将其修复为 \\d（2个反斜杠字符）
      let fixedRule = inputRule;
      
      // 检查是否包含双重转义的模式
      if (inputRule.includes('\\\\d') || inputRule.includes('\\\\b')) {
        // 将 \\\\d 替换为 \\d，\\\\b 替换为 \\b 等
        fixedRule = inputRule
          .replace(/\\\\d/g, '\\d')   // \\\\d → \\d
          .replace(/\\\\b/g, '\\b')   // \\\\b → \\b  
          .replace(/\\\\s/g, '\\s')   // \\\\s → \\s
          .replace(/\\\\w/g, '\\w')   // \\\\w → \\w
          .replace(/\\\\W/g, '\\W')   // \\\\W → \\W
          .replace(/\\\\D/g, '\\D')   // \\\\D → \\D
          .replace(/\\\\S/g, '\\S');  // \\\\S → \\S
        
        console.log('🔧 规则标准化修复 (双重转义修复):', {
          原始规则: inputRule,
          修复后规则: fixedRule,
          修复前反斜杠数量: (inputRule.match(/\\/g) || []).length,
          修复后反斜杠数量: (fixedRule.match(/\\/g) || []).length,
          修复类型: '双重转义修复',
          修复了: inputRule !== fixedRule
        });
        return fixedRule;
      }
      
      return inputRule;
    };
    
    // 🔍 标准化输入规则
    const normalizedRule = normalizeRule(rule);
    
    // 🔍 双反斜杠说明：为什么需要 \\d 而不是 \d？
    // 
    // 在JavaScript中，当我们使用字符串来构造正则表达式时，需要双重转义：
    // 
    // 1️⃣ JavaScript字符串解析：'\\d' → '\d' (字符串内容)
    // 2️⃣ RegExp构造函数解析：'\d' → /\d/ (正则表达式对象)
    // 
    // 例如：
    // ❌ 错误：new RegExp('\d{6}')   → 会报错，因为\d在字符串中是无效转义
    // ✅ 正确：new RegExp('\\d{6}')  → 等同于 /\d{6}/
    // ✅ 正确：/\d{6}/              → 直接使用正则字面量，只需要一个反斜杠
    //
    // 验证函数（可在控制台测试）：
    const validateRegexPattern = (patternStr, testString = '123456') => {
      try {
        const regex = new RegExp(patternStr);
        console.log(`✅ 模式验证成功: "${patternStr}" → ${regex.toString()}`);
        console.log(`📝 测试字符串 "${testString}" 匹配结果:`, testString.match(regex));
        return regex;
      } catch (error) {
        console.log(`❌ 模式验证失败: "${patternStr}" → ${error.message}`);
        return null;
      }
    };
    
    // 📝 正则模式定义（使用双反斜杠，因为这些是字符串，会传给RegExp构造函数）
    const patterns = {
      digits6: '\\d{6}',           // → /\d{6}/ 连续6个数字
      digits4: '\\d{4}',           // → /\d{4}/ 连续4个数字  
      digits4to8: '\\d{4,8}',      // → /\d{4,8}/ 连续4到8个数字
      digits4plus: '\\d{4,}',      // → /\d{4,}/ 4个及以上数字
      alphaNum6: '[\\d0-9A-Z]{6}', // → /[\d0-9A-Z]{6}/ 6个大写字母及数字
      alphaNum10: '[\\d0-9a-zA-Z]{10}', // → /[\d0-9a-zA-Z]{10}/ 10个大小写及数字
      colonDigits: '(?<=[：:]\\s*)\\d{4,8}(?!\\d)', // → /(?<=[：:]\s*)\d{4,8}(?!\d)/ 冒号后数字
      punctDigits: '(?<=[：:。.])\\d{6}',  // → /(?<=[：:。.])\d{6}/ 标点后数字
      wordBoundary6: '\\b\\d{6}\\b',       // → /\b\d{6}\b/ 6位数字(单词边界)
      codeKeyword: '验证码[：:\\s]*\\d{4,8}',  // → /验证码[：:\s]*\d{4,8}/ 验证码字样后数字
      codeEnglish: 'code[：:\\s]*\\d{4,8}'    // → /code[：:\s]*\d{4,8}/ code字样后数字
    };
    
    // 📋 规则映射表（结构更清晰）- 包含原始和修复后的两种格式
    const ruleMap = {
      // 基础数字提取
      [buildBodyTextRule(patterns.digits6)]: '连续6个数字',
      [buildBodyTextRule(patterns.digits4)]: '连续4个数字', 
      [buildBodyTextRule(patterns.digits4to8)]: '连续4到8个数字',
      [buildBodyTextRule(patterns.digits4plus)]: '4个及以上数字',
      
      // 邮件主题提取
      [buildTitleRule(patterns.digits6)]: '主题中连续6个数字',
      [buildTitleRule(patterns.digits4)]: '主题中连续4个数字',
      [buildTitleRule(patterns.digits4to8)]: '主题中连续4到8个数字',
      
      // 智能定位提取
      [buildBodyTextRule(patterns.colonDigits)]: '冒号后连续4到8个数字',
      [buildBodyClearRule(patterns.punctDigits)]: '标点后连续6个数字',
      
      // 字母数字组合
      [buildBodyTextRule(patterns.alphaNum6)]: '连续6个大写字母及数字',
      [buildBodyTextRule(patterns.alphaNum10)]: '连续10个大小写及数字',
      
      // 特殊提取
      '$BODY|<a href="|"$': '提取第一个链接',
      '$BODYTEXT$': '完整邮件内容',
      
      // 扩展规则
      [buildBodyTextRule(patterns.wordBoundary6)]: '6位数字(单词边界)',
      [buildBodyTextRule(patterns.codeKeyword)]: '验证码字样后数字',
      [buildBodyTextRule(patterns.codeEnglish)]: 'code字样后数字'
    };
    
        // 🔍 调试信息 - 显示规则解析过程
    if (rule && rule.includes('\\d')) {
      console.log('📋 规则解析调试:', {
        输入规则: rule,
        标准化规则: normalizedRule,
        找到的描述: ruleMap[normalizedRule] || '未知规则',
        规则类型: normalizedRule ? (normalizedRule.startsWith('$BODYTEXT-R|') ? 'BODYTEXT正则' : 
                 normalizedRule.startsWith('$TITLE-R|') ? 'TITLE正则' : 
                 normalizedRule.startsWith('$BODYCLEAR-R|') ? 'BODYCLEAR正则' : '其他') : '无效',
        是否需要修复: rule !== normalizedRule
      });
    }
    
    // 🎯 优先使用标准化后的规则查找描述
    return ruleMap[normalizedRule] || ruleMap[rule] || rule;
  };

  // 心蓝API调用函数 - checkMail+sent参数模式，可搜索已存在邮件
  const checkSingleEmail = async (email) => {
    try {
      // 添加小延迟以改善用户体验，让用户看到搜索过程
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 直接使用当前React状态，不再检查localStorage一致性
      const configRule = settings.extractRule;

      // 🔧 关键修复：确保API参数格式正确
      const params = new URLSearchParams();
      
      // 添加必要参数
      params.append('uid', apiConfig.uid);
      params.append('sign', apiConfig.sign);
      params.append('act', 'checkMail');  // 恢复使用checkMail，正确设置sent参数可获取已存在邮件
      params.append('email', email);
      params.append('from', '');
      params.append('fields', configRule); // 直接使用React状态中的规则
      params.append('sent', String(-parseInt(settings.timeRange) * 60));  // 使用负整数格式，更可靠
      params.append('t', String(Date.now()));
      
      // 只有当标题过滤不为空时才添加title参数
      if (settings.titleFilter && settings.titleFilter.trim() !== '') {
        params.append('title', settings.titleFilter.trim());
      }

      // 🔍 增强调试：显示完整URL和参数详情
      const fullUrl = `https://bsh.bhdata.com:30015/bhmailer?${params}`;
      const timeRangeMinutes = parseInt(settings.timeRange);
      const sentSeconds = -timeRangeMinutes * 60;
      
      console.log('🌐 完整API调用详情:', { 
        email, 
        完整URL: fullUrl,
        分解参数: {
          uid: params.get('uid'),
          sign: params.get('sign'),
          act: params.get('act'),
          email: params.get('email'),
          title: params.get('title'),
          from: params.get('from'),
          fields: params.get('fields'),
          sent: params.get('sent'),
          t: params.get('t')
        },
        原始配置规则: configRule,
        URL编码后的fields: encodeURIComponent(configRule),
        时间范围说明: `${timeRangeMinutes}分钟 (sent=${sentSeconds}秒)`,
        请求时间戳: new Date().toISOString()
      });
      
      // 🔧 额外验证：检查正则表达式是否有效
      if (configRule && configRule.startsWith('$BODYTEXT-R|') && configRule.endsWith('$')) {
        const regexStr = configRule.slice(12, -1);
        try {
          const testRegex = new RegExp(regexStr);
          console.log('✅ 提取规则验证通过:', {
            原始规则: configRule,
            解析的正则: regexStr,
            正则对象: testRegex.toString(),
            测试匹配: testRegex.test('123456')
          });
        } catch (regexError) {
          console.error('❌ 提取规则格式错误:', {
            规则: configRule,
            正则字符串: regexStr,
            错误: regexError.message
          });
        }
      }

      const response = await fetch(fullUrl, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // 🔍 增强API响应调试
      console.log('📨 API响应完整分析:', {
        email,
        响应状态: response.status,
        响应头: Object.fromEntries(response.headers.entries()),
        响应数据: data,
        数据类型分析: {
          code类型: typeof data.code,
          code值: data.code,
          msg类型: typeof data.msg,
          msg值: data.msg,
          msg长度: data.msg ? data.msg.length : 0,
          msg是否为空字符串: data.msg === '',
          msg是否为null: data.msg === null,
          msg是否为undefined: data.msg === undefined
        },
        预期结果: data.code === 0 ? '成功需要检查msg内容' : '失败或未找到'
      });
      
      // 🚨 特殊处理：API返回成功但验证码为空的情况
      if (data.code === 0 && (!data.msg || data.msg.trim() === '')) {
        console.warn('🚨 检测到API成功但验证码为空的问题:', {
          可能原因: [
            '1. 邮件内容不匹配提取规则',
            '2. 提取规则格式问题',
            '3. 心蓝API的fields参数处理异常',
            '4. 邮件确实没有符合规则的内容'
          ],
          建议排查步骤: [
            '1. 检查邮件原始内容',
            '2. 验证提取规则正确性', 
            '3. 尝试不同的提取规则',
            '4. 检查时间范围设置'
          ],
          当前设置: {
            提取规则: configRule,
            时间范围: `${timeRangeMinutes}分钟`,
            邮箱: email
          }
        });
        
        // 🔧 自动诊断：获取完整邮件内容用于分析
        console.log('🔍 开始自动诊断：获取完整邮件内容...');
        try {
          const diagnosticParams = new URLSearchParams();
          
          // 添加必要参数
          diagnosticParams.append('uid', apiConfig.uid);
          diagnosticParams.append('sign', apiConfig.sign);
          diagnosticParams.append('act', 'checkMail');
          diagnosticParams.append('email', email);
          diagnosticParams.append('from', '');
          diagnosticParams.append('fields', '$BODYTEXT$'); // 获取完整邮件内容
          diagnosticParams.append('sent', String(-parseInt(settings.timeRange) * 60));
          diagnosticParams.append('t', String(Date.now()));
          
          // 只有当标题过滤不为空时才添加title参数
          if (settings.titleFilter && settings.titleFilter.trim() !== '') {
            diagnosticParams.append('title', settings.titleFilter.trim());
          }
          
          const diagnosticUrl = `https://bsh.bhdata.com:30015/bhmailer?${diagnosticParams}`;
          console.log('🔍 诊断请求URL:', diagnosticUrl);
          
          const diagnosticResponse = await fetch(diagnosticUrl, {
            method: 'GET',
            mode: 'cors',
            headers: {
              'Accept': 'application/json',
            }
          });
          
          if (diagnosticResponse.ok) {
            const diagnosticData = await diagnosticResponse.json();
            console.log('📧 完整邮件内容获取结果:', {
              code: diagnosticData.code,
              messageLength: diagnosticData.msg ? diagnosticData.msg.length : 0,
              messagePreview: diagnosticData.msg ? diagnosticData.msg.substring(0, 200) + '...' : '无内容',
              fullMessage: diagnosticData.msg
            });
            
            if (diagnosticData.code === 0 && diagnosticData.msg) {
              // 🎯 在完整内容上测试当前提取规则
              console.log('🧪 在完整邮件内容上测试提取规则...');
              const extractedResult = applyExtractionRule(diagnosticData.msg, configRule);
              console.log('🧪 提取规则测试结果:', {
                原始提取规则: configRule,
                测试结果: extractedResult,
                提取成功: extractedResult && extractedResult !== '未匹配到内容',
                完整邮件可用: true
              });
              
              // 📊 返回带有完整邮件内容的结果，这样handleExtractRuleChange可以使用
              return { 
                code: data.code, 
                message: diagnosticData.msg, // 使用完整邮件内容
                diagnosticInfo: {
                  originalEmpty: true,
                  fullContentAvailable: true,
                  extractedWithCurrentRule: extractedResult
                }
              };
            } else {
              console.warn('⚠️ 诊断请求也未能获取邮件内容');
            }
          } else {
            console.warn('⚠️ 诊断请求失败:', diagnosticResponse.status);
          }
        } catch (diagnosticError) {
          console.error('❌ 诊断请求异常:', diagnosticError.message);
        }
      }
      
      return { code: data.code, message: data.msg };
    } catch (error) {
      console.error('❌ API调用失败详情:', {
        邮箱: email,
        错误类型: error.name,
        错误消息: error.message,
        错误堆栈: error.stack
      });
      return { code: -1, message: `请求失败: ${error.message}` };
    }
  };

  const handleStartQuery = async () => {
    if (!apiConfig.uid || !apiConfig.sign) {
      addLog('❌ 请先配置API信息');
      return;
    }
    
    const emailList = emails.split('\n').filter(email => email.trim());
    if (emailList.length === 0) {
      addLog('❌ 请先添加邮箱地址');
      return;
    }

    setIsProcessing(true);
    addLog(`🚀 开始查询 ${emailList.length} 个邮箱的验证码...`);
    addLog(`⏰ 查询时间范围: 最近${settings.timeRange}分钟内的邮件`);
    addLog(`📧 使用checkMail+sent参数: 可搜索已存在邮件，解决其他工具读取后无法提取的问题`);
    
    // 重置所有邮箱状态
    const initialResults = {};
    emailList.forEach(email => {
      initialResults[email.trim()] = {
        status: '🔍 检查中',
        code: '',
        message: ''
      };
    });
    setEmailResults(initialResults);

    try {
      // 并发查询所有邮箱
      const promises = emailList.map(async (email) => {
        const trimmedEmail = email.trim();
        addLog(`📧 正在检查邮箱: ${trimmedEmail} (包含已存在邮件)`);
        
        const result = await checkSingleEmail(trimmedEmail);
        
        let status, code, logMessage;
        if (result.code === 0) {
          status = '✅ 成功';
          code = result.message;
          // 增强验证码显示逻辑，确保内容可见
          if (result.message && result.message.trim()) {
            logMessage = `✅ ${trimmedEmail}: 获取到验证码 "${result.message}"`;
          } else {
            logMessage = `⚠️ ${trimmedEmail}: API返回成功但验证码为空`;
            console.warn('API返回成功但验证码为空:', { email: trimmedEmail, result });
          }
        } else if (result.code === 7) {
          status = '⚠️ 未找到';
          code = '未找到邮件';
          logMessage = `⚠️ ${trimmedEmail}: 未找到验证码邮件`;
        } else {
          status = '❌ 失败';
          code = result.message;
          logMessage = `❌ ${trimmedEmail}: ${result.message}`;
        }

        // 🔧 修复并发状态更新的竞态条件 - 使用函数式更新和原子性操作
        setEmailResults(prev => {
          const newResult = { 
            status, 
            code, 
            message: result.message,
            // 🔥 关键修复：确保原始邮件内容在首次获取时就被保存
            originalMessage: result.message, // 保存原始邮件内容，用于后续提取规则切换
            timestamp: Date.now(),
            extractedWith: settings.extractRule // 记录首次使用的提取规则
          };
          
          // 验证结果完整性和冲突检测
          if (result.code === 0 && (!code || !code.trim())) {
            console.warn('警告：API返回成功但验证码为空，保持原有结果', {
              email: trimmedEmail,
              newResult,
              previousResult: prev[trimmedEmail],
              是否有原始内容: !!result.message && result.message.trim().length > 0
            });
            // 如果新结果的验证码为空但状态是成功，保持原有结果（如果存在）
            if (prev[trimmedEmail] && prev[trimmedEmail].code && prev[trimmedEmail].code.trim()) {
              addLog(`🔄 ${trimmedEmail}: 检测到空结果，保持原有验证码`);
              return prev; // 不更新，保持原有结果
            }
          }
          
          // 🛡️ 防止并发更新冲突：检查当前状态是否还是"检查中"
          const currentEmailState = prev[trimmedEmail];
          if (currentEmailState && currentEmailState.status !== '🔍 检查中' && 
              currentEmailState.status !== '⚠️ 未找到' && 
              currentEmailState.code && currentEmailState.code.trim() &&
              result.code !== 0) {
            // 如果当前邮箱已有成功结果，且新结果不是成功，则保持原有结果
            console.log(`🛡️ ${trimmedEmail}: 防止覆盖已有成功结果`, {
              current: currentEmailState,
              new: newResult
            });
            return prev;
          }
          
          // 🔄 原子性更新：确保状态一致性
          const updatedResults = {
          ...prev,
            [trimmedEmail]: newResult
          };
          
          // 调试日志：记录状态更新
          console.log(`📝 状态更新 ${trimmedEmail}:`, {
            from: currentEmailState?.status || '初始状态',
            to: newResult.status,
            code: newResult.code || '无',
            hasOriginalMessage: !!newResult.originalMessage,
            originalMessageLength: newResult.originalMessage ? newResult.originalMessage.length : 0,
            timestamp: newResult.timestamp
          });
          
          return updatedResults;
        });

        addLog(logMessage);
        return { email: trimmedEmail, result };
      });

      const results = await Promise.all(promises);
      
      // 🔧 修复成功计数 - 从Promise结果中直接计算，避免状态更新延迟
      const successCount = results.filter(r => r.result.code === 0).length;
      const failureCount = results.filter(r => r.result.code === -1).length;
      const notFoundCount = results.filter(r => r.result.code === 7).length;
      
      addLog(`🎉 批量查询完成！成功: ${successCount}, 未找到: ${notFoundCount}, 失败: ${failureCount}`);
      
    } catch (error) {
      addLog(`❌ 查询过程出错: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const copyVerificationCode = (email) => {
    const result = emailResults[email];
    if (result && result.code && result.code !== '未找到邮件') {
      navigator.clipboard.writeText(result.code).then(() => {
        addLog(`📋 已复制验证码: ${result.code} (邮箱: ${email})`);
        // 更新状态显示已复制
        setEmailResults(prev => ({
          ...prev,
          [email]: { ...prev[email], status: '📋 已复制' }
        }));
      }).catch(err => {
        addLog(`❌ 复制失败: ${err.message}`);
      });
    } else {
      addLog(`⚠️ 邮箱 ${email} 暂无验证码可复制`);
    }
  };

  const retryEmail = async (email) => {
    if (isProcessing) {
      addLog('⚠️ 正在处理中，请等待完成后再试');
      return;
    }

    addLog(`🔄 重新检查邮箱: ${email}`);
    
    // 更新状态为检查中
    setEmailResults(prev => ({
      ...prev,
      [email]: { status: '🔍 检查中', code: '', message: '' }
    }));

    const result = await checkSingleEmail(email);
    
    let status, code, logMessage;
    if (result.code === 0) {
      status = '✅ 成功';
      code = result.message;
      // 增强重试时的验证码显示
      if (result.message && result.message.trim()) {
        logMessage = `✅ ${email}: 重试成功，获取到验证码 "${result.message}"`;
      } else {
        logMessage = `⚠️ ${email}: 重试成功但验证码为空`;
        console.warn('重试成功但验证码为空:', { email, result });
      }
    } else if (result.code === 7) {
      status = '⚠️ 未找到';
      code = '未找到邮件';
      logMessage = `⚠️ ${email}: 重试后仍未找到验证码邮件`;
    } else {
      status = '❌ 失败';
      code = result.message;
      logMessage = `❌ ${email}: 重试失败 - ${result.message}`;
    }

    // 🔧 更新重试结果 - 增强状态验证和原子性更新
    setEmailResults(prev => {
      const newResult = { 
        status, 
        code, 
        message: result.message,
        // 🔥 关键修复：重试时也要保存原始邮件内容
        originalMessage: result.message, // 保存原始邮件内容
        timestamp: Date.now(),
        retryCount: (prev[email]?.retryCount || 0) + 1, // 记录重试次数
        extractedWith: settings.extractRule // 记录重试时使用的提取规则
      };
      
      // 验证重试结果完整性
      if (result.code === 0 && (!code || !code.trim())) {
        console.warn('警告：重试API返回成功但验证码为空', {
          email,
          newResult,
          previousResult: prev[email],
          是否有原始内容: !!result.message && result.message.trim().length > 0
        });
        // 如果新结果为空但之前有正确结果，保持原有结果
        if (prev[email] && prev[email].code && prev[email].code.trim()) {
          addLog(`🔄 ${email}: 重试检测到空结果，保持原有验证码`);
          return prev;
        }
      }
      
      // 🔄 原子性重试更新
      const updatedResults = {
      ...prev,
        [email]: newResult
      };
      
      // 调试日志：重试状态更新
      console.log(`🔄 重试状态更新 ${email}:`, {
        retryCount: newResult.retryCount,
        newStatus: newResult.status,
        newCode: newResult.code || '无',
        hasOriginalMessage: !!newResult.originalMessage,
        originalMessageLength: newResult.originalMessage ? newResult.originalMessage.length : 0
      });
      
      return updatedResults;
    });

    addLog(logMessage);
  };

  const deleteEmail = (email) => {
    const emailList = emails.split('\n').filter(e => e.trim() !== email);
    setEmails(emailList.join('\n'));
    
    // 删除结果
    setEmailResults(prev => {
      const newResults = { ...prev };
      delete newResults[email];
      return newResults;
    });
    
    addLog(`🗑️ 已删除邮箱: ${email}`);
  };

  const copyAllCodes = () => {
    const codes = [];
    Object.entries(emailResults).forEach(([email, result]) => {
      if (result.code && result.code !== '未找到邮件') {
        codes.push(`${email}: ${result.code}`);
      }
    });

    if (codes.length > 0) {
      navigator.clipboard.writeText(codes.join('\n')).then(() => {
        addLog(`📋 已复制 ${codes.length} 个验证码到剪贴板`);
      }).catch(err => {
        addLog(`❌ 批量复制失败: ${err.message}`);
      });
    } else {
      addLog('⚠️ 暂无验证码可复制');
    }
  };

  const clearResults = () => {
    setEmailResults({});
    addLog('🗑️ 已清空所有验证码结果');
  };

  // 🔧 应用重置功能 - 彻底清理所有配置和状态
  const resetApplication = () => {
    if (window.confirm('⚠️ 确定要重置应用吗？\n\n这将清除：\n• 所有保存的配置\n• 所有邮箱列表\n• 所有查询结果\n• 所有历史日志\n\n此操作不可撤销！')) {
      try {
        // 清理所有可能的localStorage键
        const allKeys = Object.keys(localStorage);
        const configKeys = allKeys.filter(key => 
          key.includes('emailVerifier') || 
          key.includes('xinlan') || 
          key.includes('mailVerifier') ||
          key.includes('bhdata')
        );
        
        configKeys.forEach(key => {
          localStorage.removeItem(key);
          console.log(`已清理存储键: ${key}`);
        });
        
        // 重置所有React状态到初始值
        setApiConfig({ uid: '', sign: '' });
        setEmails('');
        setSettings({
          titleFilter: '验证码',
          timeRange: '10',  // 扩大到10分钟，防止新邮件导致旧验证码丢失
          extractRule: '$BODYTEXT-R|\\d{6}$'
        });
        setEmailResults({});
        setLogs([]);
        setIsProcessing(false);
        setIsConfigLoaded(false);
        
        // 添加重置成功日志
        setTimeout(() => {
          addLog('🔄 应用已重置，所有配置和数据已清空');
          addLog('✅ 应用恢复到初始状态，请重新配置');
        }, 100);
        
        console.log('应用重置完成:', {
          清理的配置键: configKeys,
          当前版本: CONFIG_VERSION,
          重置时间: new Date().toISOString()
        });
        
      } catch (error) {
        console.error('应用重置失败:', error);
        addLog(`❌ 应用重置失败: ${error.message}`);
      }
    }
  };

  const emailList = emails.split('\n').filter(email => email.trim());

  // 窗口控制函数
  const minimizeWindow = () => {
    if (window.electron) {
      window.electron.minimize();
    }
  };

  const maximizeWindow = () => {
    if (window.electron) {
      window.electron.maximize();
    }
  };

  const closeWindow = () => {
    if (window.electron) {
      window.electron.close();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden">
      {/* 精美的背景装饰 */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%239C92AC%22%20fill-opacity%3D%220.02%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>
      
      {/* 自定义标题栏 */}
      <div className="relative z-50 h-8 bg-white/80 backdrop-blur-xl border-b border-gray-200/50 flex items-center justify-between px-4 select-none" style={{WebkitAppRegion: 'drag'}}>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded flex items-center justify-center shadow-sm">
            <img 
              src="./icon-16x16.png" 
              alt="心蓝多邮箱接码" 
              className="w-4 h-4 object-contain"
              onError={(e) => {
                // 如果图标加载失败，显示备用图标
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 items-center justify-center shadow-sm hidden">
            <svg width="12" height="12" viewBox="0 0 256 256" className="fill-white">
              <circle cx="128" cy="128" r="120" fill="currentColor"/>
              <g transform="translate(128, 128)">
                <rect x="-40" y="-20" width="80" height="50" rx="6" ry="6" fill="white" opacity="0.9"/>
                <path d="M -40 -20 L 0 15 L 40 -20" stroke="currentColor" strokeWidth="6" fill="none" strokeLinecap="round" strokeLinejoin="round"/>
                <g transform="translate(25, -30)">
                  <path d="M 0 -8 L 8 -5 L 8 5 C 8 8 5 10 0 12 C -5 10 -8 8 -8 5 L -8 -5 Z" fill="white"/>
                  <path d="M -4 0 L -1 2 L 4 -3" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"/>
                </g>
              </g>
            </svg>
            </div>
          </div>
          <span className="text-sm font-semibold text-gray-700">心蓝多邮箱接码</span>
        </div>
        
        {/* 窗口控制按钮 */}
        <div className="flex items-center gap-1" style={{WebkitAppRegion: 'no-drag'}}>
          <button
            onClick={minimizeWindow}
            className="w-4 h-4 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-sm hover:shadow-md"
          >
          </button>
          <button
            onClick={maximizeWindow}
            className="w-4 h-4 rounded-full bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 transition-all duration-200 shadow-sm hover:shadow-md"
          >
          </button>
          <button
            onClick={closeWindow}
            className="w-4 h-4 rounded-full bg-gradient-to-r from-red-400 to-red-500 hover:from-red-500 hover:to-red-600 transition-all duration-200 shadow-sm hover:shadow-md"
          >
          </button>
        </div>
      </div>
      
      <div className="relative flex h-[calc(100vh-2rem)] overflow-hidden">
        {/* 左侧配置面板 - 超紧凑设计，一眼看完所有配置 */}
        <div className="w-80 bg-white/90 backdrop-blur-xl border-r border-gray-200/60 flex flex-col h-full overflow-hidden shadow-2xl">
          
          {/* 配置内容区域 - 优化间距布局，移除滚动条 */}
          <div className="flex-1 overflow-hidden p-2 pt-3 space-y-2">
            
            {/* API配置区域 - 绿色主题 */}
            <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200/60 shadow-lg">
              <CardContent className="p-2">
                <div className="flex items-center gap-1 mb-1">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                    <span className="text-xs text-white">🔑</span>
                  </div>
                  <h3 className="text-xs font-semibold text-green-700">API配置</h3>
                </div>
                <div className="space-y-1">
                  <div>
                    <label className="text-xs text-green-600 block mb-0.5">授权ID</label>
                    <Input
                      value={apiConfig.uid}
                      onChange={(e) => setApiConfig({...apiConfig, uid: e.target.value})}
                      className="h-6 text-xs border-green-200 focus:border-green-400"
                      placeholder="请输入授权ID"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-green-600 block mb-0.5">API密钥</label>
                    <Input
                      type="password"
                      value={apiConfig.sign}
                      onChange={(e) => setApiConfig({...apiConfig, sign: e.target.value})}
                      className="h-6 text-xs border-green-200 focus:border-green-400"
                      placeholder="请输入API密钥"
                    />
                  </div>
                  {/* 配置管理按钮 - 重新设计为3个按钮 */}
                  <div className="space-y-1 mt-1">
                    <div className="flex gap-1">
                      <Button 
                        variant="outline"
                        onClick={saveConfig}
                        className="flex-1 h-5 text-xs border-green-300 text-green-600 hover:bg-green-50"
                      >
                        💾 保存配置
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => {
                          if (window.confirm('确定要清除所有保存的配置吗？')) {
                            localStorage.removeItem(CONFIG_KEY);
                            setApiConfig({ uid: '', sign: '' });
                            setEmails('');
                            setSettings({
                              titleFilter: '验证码',
                              timeRange: '10',
                              extractRule: '$BODYTEXT-R|\\d{6}$'
                            });
                            setEmailResults({});
                            addLog('🗑️ 配置已清除');
                          }
                        }}
                        className="flex-1 h-5 text-xs border-green-300 text-green-600 hover:bg-green-50"
                      >
                        🗑️ 清除配置
                      </Button>
                    </div>
                    {/* 🔧 新增重置应用按钮 */}
                    <Button 
                      variant="outline"
                      onClick={resetApplication}
                      className="w-full h-5 text-xs border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                    >
                      🔄 重置应用
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 邮箱管理区域 - 蓝色主题 */}
            <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200/60 shadow-lg">
              <CardContent className="p-2">
                <div className="flex items-center gap-1 mb-1">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                    <span className="text-xs text-white">📧</span>
                  </div>
                  <h3 className="text-xs font-semibold text-blue-700">邮箱管理</h3>
                </div>
                <div className="space-y-1">
                  <label className="text-xs text-blue-600 block mb-0.5">邮箱列表</label>
                  <Textarea
                    value={emails}
                    onChange={(e) => setEmails(e.target.value)}
                    className="h-12 text-xs border-blue-200 focus:border-blue-400 resize-none"
                    placeholder="每行一个邮箱地址"
                  />
                  <div className="flex gap-1">
                    <Button 
                      onClick={() => addLog(`➕ 当前有 ${emailList.length} 个邮箱`)}
                      className="flex-1 h-5 text-xs bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 px-2"
                    >
                      ➕ 添加
                    </Button>
                    <Button 
                      onClick={() => {
                        setEmails('');
                        setEmailResults({});
                        addLog('🗑️ 邮箱列表已清空');
                      }}
                      variant="outline"
                      className="flex-1 h-5 text-xs border-blue-300 text-blue-600 hover:bg-blue-50 px-2"
                    >
                      🗑️ 清空
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 参数设置区域 - 紫色主题 */}
            <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200/60 shadow-lg">
              <CardContent className="p-2">
                <div className="flex items-center gap-1 mb-1">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                    <span className="text-xs text-white">⚙️</span>
                  </div>
                  <h3 className="text-xs font-semibold text-purple-700">参数设置</h3>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <div>
                    <label className="text-xs text-purple-600 block mb-0.5">邮件标题过滤（可选）</label>
                    <Input
                      value={settings.titleFilter}
                      onChange={(e) => setSettings({...settings, titleFilter: e.target.value})}
                      className="h-6 text-xs border-purple-200 focus:border-purple-400"
                      placeholder="留空表示不过滤标题"
                    />
                  </div>
                  <div>
                    <label className="text-xs text-purple-600 block mb-0.5">时间范围</label>
                    <div className="flex items-center gap-1">
                      <Input
                        value={settings.timeRange}
                        onChange={(e) => setSettings({...settings, timeRange: e.target.value})}
                        className="h-6 text-xs border-purple-200 focus:border-purple-400 w-16"
                        type="number"
                      />
                      <span className="text-xs text-purple-700 font-medium whitespace-nowrap">分钟</span>
                    </div>
                  </div>
                </div>
                <div className="mt-1">
                  <label className="text-xs text-purple-600 block mb-0.5">提取规则</label>
                  <select 
                    value={settings.extractRule || '$BODYTEXT-R|\\d{6}$'}
                    onChange={(e) => handleExtractRuleChange(e.target.value)}
                    className="w-full h-6 text-xs border border-purple-200 rounded focus:border-purple-400 bg-white"
                  >
                    {/* 🔧 动态生成选项，确保JavaScript字符串与option value完全一致 */}
                    <optgroup label="📧 邮件内容数字">
                      {[
                        { value: '$BODYTEXT-R|\\d{6}$', label: '6位数字验证码' },
                        { value: '$BODYTEXT-R|\\d{4}$', label: '4位数字验证码' },
                        { value: '$BODYTEXT-R|\\d{4,8}$', label: '4-8位数字' },
                        { value: '$BODYTEXT-R|\\d{4,}$', label: '4个及以上数字' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                    <optgroup label="📋 邮件主题提取">
                      {[
                        { value: '$TITLE-R|\\d{6}$', label: '主题中6位数字' },
                        { value: '$TITLE-R|\\d{4}$', label: '主题中4位数字' },
                        { value: '$TITLE-R|\\d{4,8}$', label: '主题中4-8位数字' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                    <optgroup label="🎯 智能定位提取">
                      {[
                        { value: '$BODYTEXT-R|[：:]\\s*\\d{4,8}$', label: '冒号后4-8位数字' },
                        { value: '$BODYCLEAR-R|[：:。.]\\d{6}$', label: '标点后6位数字' },
                        { value: '$BODYTEXT-R|验证码[：:\\s]*\\d{4,8}$', label: '验证码字样后数字' },
                        { value: '$BODYTEXT-R|code[：:\\s]*\\d{4,8}$', label: 'code字样后数字' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                    <optgroup label="📏 带分隔符的验证码">
                      {[
                        { value: '$BODYTEXT-R|\\d{3}\\s*\\d{3}$', label: '3+3位数字(带空格)' },
                        { value: '$BODYTEXT-R|\\d{3}[-_]\\d{3}$', label: '3-3位数字(带分隔符)' },
                        { value: '$BODYTEXT-R|\\d{2}\\s*\\d{2}\\s*\\d{2}$', label: '2+2+2位数字(带空格)' },
                        { value: '$BODYTEXT-R|\\d{1}\\s*\\d{1}\\s*\\d{1}\\s*\\d{1}\\s*\\d{1}\\s*\\d{1}$', label: '6位数字(每位带空格)' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                    <optgroup label="🔤 字母数字组合">
                      {[
                        { value: '$BODYTEXT-R|[0-9A-Z]{6}$', label: '6位大写字母数字' },
                        { value: '$BODYTEXT-R|[0-9A-Z]{8}$', label: '8位大写字母数字' },
                        { value: '$BODYTEXT-R|[0-9a-zA-Z]{10}$', label: '10位字母数字混合' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                    <optgroup label="🔗 链接和完整内容">
                      {[
                        { value: '$BODY|<a href="|"$', label: '提取第一个链接' },
                        { value: '$BODYTEXT$', label: '完整邮件内容' }
                      ].map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </optgroup>
                  </select>
                </div>
              </CardContent>
            </Card>
            
            {/* 日志区域 - 灰色主题，固定高度平衡显示 */}
            <Card className="bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200/60 shadow-lg h-44 shrink-0">
              <CardContent className="p-2 h-full flex flex-col">
                <div className="flex items-center gap-1 mb-1 shrink-0">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center">
                    <span className="text-xs text-white">📝</span>
                  </div>
                  <h3 className="text-xs font-semibold text-gray-700">实时日志</h3>
                </div>
                <div className="flex-1 bg-gray-100 rounded p-1 text-xs text-gray-700 overflow-y-auto font-mono">
                  {logs.map((log, index) => (
                    <div key={index} className="leading-tight whitespace-pre-wrap">
                      {log}
                    </div>
                  ))}
                  {logs.length === 0 && (
                    <div className="text-gray-400">等待操作日志...</div>
                  )}
                  <div ref={logEndRef} />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 底部操作按钮区域 - 固定高度 */}
          <div className="p-2 flex-shrink-0">
            <Button 
              onClick={handleStartQuery}
              disabled={isProcessing}
              className="w-full h-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold text-sm transition-all duration-300"
            >
              {isProcessing ? (
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>查询中...</span>
                </div>
              ) : (
                '🚀 开始查询'
              )}
              </Button>
          </div>
        </div>

        {/* 右侧主内容区域 - 紧凑设计 */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          <div className="flex-1 p-4 overflow-hidden">
            <div className="h-full bg-white/70 backdrop-blur-xl rounded-xl shadow-xl border border-gray-200/50 flex flex-col overflow-hidden">
              {/* 头部标题栏 - 减小高度 */}
              <div className="px-4 py-3 border-b border-gray-100/50 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center shadow-sm border border-blue-200/30">
                      <span className="text-white text-xs">📋</span>
                    </div>
                    <div>
                      <h2 className="text-sm font-bold text-gray-700">
                        查询结果
                      </h2>
                      <p className="text-xs text-gray-500">
                        共 {emailList.length} 个邮箱
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-7 text-xs px-3 bg-white/60 hover:bg-blue-50 border-blue-200/60 text-blue-700 font-medium rounded-md shadow-sm transition-all duration-200"
                      onClick={copyAllCodes}
                    >
                      📋 复制全部
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="h-7 text-xs px-3 bg-white/60 hover:bg-red-50 border-red-200/60 text-red-600 font-medium rounded-md shadow-sm transition-all duration-200"
                      onClick={clearResults}
                    >
                      🗑️ 清空结果
                    </Button>
                  </div>
                </div>
              </div>

              {/* 结果列表 - 紧凑显示 */}
              <div className="flex-1 overflow-y-auto p-3">
                <div className="space-y-2">
                  {emailList.map((email, index) => {
                    const trimmedEmail = email.trim();
                    const result = emailResults[trimmedEmail];
                    
                    return (
                      <div key={index} className="group bg-white/80 backdrop-blur-sm rounded-lg p-3 border border-gray-200/50 shadow-sm hover:shadow-md transition-all duration-300 hover:border-blue-200/60">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-6 h-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full flex items-center justify-center text-blue-700 text-xs font-bold shadow-sm border border-blue-200/50">
                              {index + 1}
                            </div>
                            <div>
                              <div className="text-sm font-semibold text-gray-800">{trimmedEmail}</div>
                              <div className="text-xs text-gray-500 mt-0.5">
                                {result ? result.status : '⏳ 等待查询'}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {result && result.code && result.code !== '未找到邮件' && (
                              <div className="px-2 py-1 text-xs bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-md border border-green-200/50 shadow-sm">
                                {/* 🎨 特殊处理：完整邮件内容使用HTML渲染 */}
                                                                {settings.extractRule === '$BODYTEXT$' ? (
                                  <div className="w-full flex items-center justify-between">
                                    <span className="font-medium text-emerald-700">📧 邮件内容已获取</span>
                                    <button
                                      onClick={() => {
                                        // 🔍 使用模态框显示完整邮件内容（解决Electron弹窗限制问题）
                                        setEmailDetailModal({
                                          isOpen: true,
                                          email: trimmedEmail,
                                          content: result.code
                                        });
                                      }}
                                      className="text-xs text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 px-2 py-1 rounded border border-blue-200 transition-colors duration-200 flex items-center gap-1"
                                    >
                                      <span className="text-blue-500">🔍</span> 查看完整内容
                                    </button>
                                  </div>
                                ) : (
                                  <span className="font-mono">{result.code}</span>
                                )}
                              </div>
                            )}
                            <div className="flex gap-1 opacity-70 group-hover:opacity-100 transition-opacity duration-200">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="h-6 text-xs px-2 bg-white/60 hover:bg-blue-50 border-blue-200/60 text-blue-700 font-medium rounded-md shadow-sm transition-all duration-200"
                                onClick={() => retryEmail(trimmedEmail)}
                                disabled={isProcessing}
                              >
                                🔄 重试
                              </Button>
                              {result && result.code && result.code !== '未找到邮件' && (
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  className="h-6 text-xs px-2 bg-white/60 hover:bg-green-50 border-green-200/60 text-green-700 font-medium rounded-md shadow-sm transition-all duration-200"
                                  onClick={() => copyVerificationCode(trimmedEmail)}
                                >
                                  📋 复制
                                </Button>
                              )}
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="h-6 text-xs px-2 bg-white/60 hover:bg-red-50 border-red-200/60 text-red-600 font-medium rounded-md shadow-sm transition-all duration-200"
                                onClick={() => deleteEmail(trimmedEmail)}
                              >
                                🗑️ 删除
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {emailList.length === 0 && (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                        <span className="text-2xl">📧</span>
                      </div>
                      <h3 className="text-sm font-semibold text-gray-800 mb-1">准备就绪</h3>
                      <p className="text-xs text-gray-500">请在左侧添加邮箱地址开始查询</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 🔍 完整邮件内容模态框 */}
      {emailDetailModal.isOpen && (
        <div 
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
          onClick={(e) => {
            // 点击背景关闭模态框
            if (e.target === e.currentTarget) {
              setEmailDetailModal({ isOpen: false, email: '', content: '' });
            }
          }}
        >
          <div 
            className="relative w-11/12 max-w-4xl max-h-[90vh] bg-white rounded-xl shadow-2xl overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 模态框头部 */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg">
                  <span className="text-white text-lg">📧</span>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-800">完整邮件内容</h3>
                  <p className="text-sm text-gray-600">邮箱: {emailDetailModal.email}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    // 复制邮件内容
                    navigator.clipboard.writeText(emailDetailModal.content).then(() => {
                      alert('📋 邮件内容已复制到剪贴板！');
                    }).catch(() => {
                      // 降级方案
                      const textArea = document.createElement('textarea');
                      textArea.value = emailDetailModal.content;
                      document.body.appendChild(textArea);
                      textArea.select();
                      document.execCommand('copy');
                      document.body.removeChild(textArea);
                      alert('📋 邮件内容已复制到剪贴板！');
                    });
                  }}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 shadow-sm"
                >
                  📋 复制内容
                </button>
                <button
                  onClick={() => setEmailDetailModal({ isOpen: false, email: '', content: '' })}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            {/* 模态框内容 */}
            <div className="p-4 overflow-y-auto max-h-[70vh]">
              <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-semibold text-gray-700">邮箱地址:</span>
                    <div className="text-gray-600">{emailDetailModal.email}</div>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-700">查询时间:</span>
                    <div className="text-gray-600">{new Date().toLocaleString()}</div>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-700">提取规则:</span>
                    <div className="text-gray-600">完整邮件内容</div>
                  </div>
                  <div>
                    <span className="font-semibold text-gray-700">内容长度:</span>
                    <div className="text-gray-600">{emailDetailModal.content.length} 字符</div>
                  </div>
                </div>
              </div>
              
              {/* 邮件内容展示区域 */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-100 px-3 py-2 border-b border-gray-200">
                  <span className="text-sm font-semibold text-gray-700">📄 邮件原始内容</span>
                </div>
                <div 
                  className="p-4 bg-white overflow-auto text-sm leading-relaxed"
                  style={{
                    fontFamily: 'inherit',
                    lineHeight: '1.6',
                    wordBreak: 'break-word',
                    maxHeight: '400px'
                  }}
                  dangerouslySetInnerHTML={{
                    __html: emailDetailModal.content
                      // 🔧 基本HTML清理和格式化
                      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
                      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')   // 移除style标签
                      .replace(/on\w+="[^"]*"/gi, '')                                    // 移除事件处理器
                      .replace(/javascript:/gi, '')                                     // 移除javascript:
                      .replace(/\n/g, '<br/>')                                          // 换行转为<br/>
                  }}
                />
              </div>
            </div>
            
            {/* 模态框底部 */}
            <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-2">
              <button
                onClick={() => {
                  // 打印邮件内容
                  const printWindow = window.open('', '_blank');
                  if (printWindow) {
                    printWindow.document.write(`
                      <!DOCTYPE html>
                      <html>
                        <head>
                          <title>邮件内容 - ${emailDetailModal.email}</title>
                          <meta charset="UTF-8">
                          <style>
                            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
                            .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
                            .content { word-wrap: break-word; }
                            @media print { body { margin: 0; } }
                          </style>
                        </head>
                        <body>
                          <div class="header">
                            <h2>📧 完整邮件内容</h2>
                            <p><strong>邮箱:</strong> ${emailDetailModal.email}</p>
                            <p><strong>导出时间:</strong> ${new Date().toLocaleString()}</p>
                          </div>
                          <div class="content">${emailDetailModal.content}</div>
                        </body>
                      </html>
                    `);
                    printWindow.document.close();
                    printWindow.print();
                  }
                }}
                className="px-4 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors duration-200"
              >
                🖨️ 打印
              </button>
              <button
                onClick={() => setEmailDetailModal({ isOpen: false, email: '', content: '' })}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmailVerifier; 