# ⚡ 快速上手指南

## 🎯 5分钟快速体验现代化邮箱验证码工具

### 📋 准备工作

确保您的电脑已安装：
- ✅ **Node.js 16+** ([下载地址](https://nodejs.org/))
- ✅ **Python 3.8+** ([下载地址](https://www.python.org/downloads/))

### 🚀 一键启动 (Windows 用户)

1. **解压项目文件夹**
2. **双击运行** `start-dev.bat`
3. **等待自动安装** 依赖和启动服务
4. **享受现代化体验**！

### 🔧 手动启动 (所有平台)

#### 步骤 1: 安装前端依赖
```bash
npm install
```

#### 步骤 2: 启动后端服务
```bash
cd python-api
python -m venv venv

# Windows 用户
venv\Scripts\activate

# Mac/Linux 用户  
source venv/bin/activate

pip install -r requirements.txt
python main.py
```

#### 步骤 3: 启动前端应用
```bash
# 新建终端窗口
npm run electron-dev
```

### 🎮 首次使用

#### 1️⃣ 配置 API 信息
- 在左侧面板输入您的**授权 ID** 和 **API 密钥**
- 系统会自动保存配置

#### 2️⃣ 添加邮箱地址  
- 在邮箱管理区域输入邮箱地址（每行一个）
- 点击**添加邮箱**按钮

#### 3️⃣ 开始验证
- 选择验证模式（立即查询 / 智能等待）
- 点击**开始获取**按钮
- 在右侧查看实时结果

#### 4️⃣ 复制验证码
- 找到验证码后，点击**复制**按钮
- 验证码将自动复制到剪贴板

### 🎨 界面特色

- **🌟 现代化设计** - Apple/Notion 风格界面
- **⚡ 流畅动画** - Framer Motion 动画效果  
- **📱 响应式布局** - 适配各种屏幕尺寸
- **🎯 实时状态** - 动态显示处理进度

### 🔍 常见问题

**Q: 启动时提示找不到 Node.js？**
A: 请先安装 Node.js 16+ 版本

**Q: Python 服务启动失败？**  
A: 检查 Python 版本是否为 3.8+，并确保正确激活虚拟环境

**Q: 前端页面无法加载？**
A: 确保后端服务已启动，并检查端口 3000 和 8000 是否被占用

**Q: 验证码获取失败？**
A: 检查 API 配置是否正确，网络连接是否正常

### 📚 更多功能

- **批量处理** - 支持无限数量邮箱同时验证
- **智能等待** - 自动轮询直到获取到验证码  
- **配置保存** - 自动保存用户设置
- **日志监控** - 详细的操作日志

### 🆘 获取帮助

- 📖 查看 [完整文档](README.md)
- 🐛 [报告问题](https://github.com/your-username/modern-mail-verification-tool/issues)
- 💬 [讨论交流](https://github.com/your-username/modern-mail-verification-tool/discussions)

---

**🎉 恭喜！您已完成现代化邮箱验证码工具的快速上手。** 