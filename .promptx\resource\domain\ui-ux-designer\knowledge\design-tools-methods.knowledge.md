<knowledge>
  <tools category="设计软件工具">
    ## 界面设计工具
    
    ### 专业设计软件
    - **Figma**：基于云端的协作设计平台
      - 实时协作功能
      - 组件系统和设计系统支持
      - 原型制作和交互设计
      - 插件生态丰富
      - 开发者交接便利
    
    - **Sketch**：Mac平台的矢量设计工具
      - 强大的符号系统
      - 丰富的插件支持
      - 精确的矢量编辑
      - 设计规范导出
      - 与其他工具良好集成
    
    - **Adobe XD**：Adobe的UI/UX设计工具
      - 设计和原型一体化
      - 语音原型功能
      - 与Adobe Creative Suite集成
      - 协作和分享功能
      - 插件和集成支持
    
    - **Framer**：高保真原型设计工具
      - 代码组件支持
      - 高级交互设计
      - 真实数据集成
      - 响应式设计
      - 团队协作功能
    
    ### 原型工具
    - **Axure RP**：专业的原型设计工具
      - 复杂交互逻辑
      - 动态面板和条件逻辑
      - 详细的功能规格
      - 团队协作功能
    
    - **Principle**：动效原型工具
      - 时间轴动画
      - 触摸交互
      - 驱动参数
      - 预览和分享
    
    - **ProtoPie**：传感器原型工具
      - 传感器输入支持
      - 复杂交互逻辑
      - 跨设备连接
      - 真实设备测试
    
    ### 用户研究工具
    - **Miro/Mural**：在线白板协作工具
      - 用户旅程映射
      - 亲和图分析
      - 头脑风暴
      - 远程协作
    
    - **Maze**：用户测试平台
      - 无主持用户测试
      - 原型测试
      - 问卷调研
      - 数据分析
    
    - **Hotjar**：用户行为分析
      - 热力图分析
      - 用户录屏
      - 反馈收集
      - 转化漏斗分析
  </tools>
  
  <methods category="设计方法论">
    ## 用户研究方法
    
    ### 定性研究方法
    - **用户访谈**
      - 结构化访谈：预设问题清单
      - 半结构化访谈：灵活的对话引导
      - 深度访谈：深入挖掘用户动机
      - 执行要点：
        - 准备开放性问题
        - 创造轻松的访谈环境
        - 避免引导性问题
        - 记录关键洞察
    
    - **用户观察**
      - 自然观察：真实环境中观察用户行为
      - 实验室观察：控制环境下的行为研究
      - 参与式观察：研究者参与用户活动
      - 执行要点：
        - 最小化干扰
        - 记录行为模式
        - 关注异常行为
        - 分析行为动机
    
    - **焦点小组**
      - 群体讨论和互动
      - 收集多元化观点
      - 探索群体共识
      - 执行要点：
        - 精心选择参与者
        - 引导而非主导讨论
        - 鼓励不同观点
        - 管理群体动态
    
    ### 定量研究方法
    - **问卷调研**
      - 大样本数据收集
      - 统计分析支持
      - 趋势识别
      - 执行要点：
        - 设计清晰的问题
        - 避免偏向性问题
        - 合理的样本大小
        - 有效的分析方法
    
    - **A/B测试**
      - 对比不同设计方案
      - 数据驱动的决策
      - 持续优化
      - 执行要点：
        - 明确测试假设
        - 控制变量
        - 足够的样本量
        - 统计显著性验证
    
    - **数据分析**
      - 用户行为数据挖掘
      - 使用模式识别
      - 性能指标监控
      - 执行要点：
        - 建立指标体系
        - 定期数据审查
        - 趋势分析
        - 数据驱动的洞察
    
    ### 设计评估方法
    - **可用性测试**
      - 任务完成率测试
      - 错误率分析
      - 效率评估
      - 满意度调研
    
    - **启发式评估**
      - 基于可用性原则的评估
      - 专家审查
      - 问题识别和优先级排序
      - 改进建议
    
    - **认知走查**
      - 模拟用户认知过程
      - 任务流程分析
      - 认知负载评估
      - 学习曲线分析
  </methods>
  
  <workflows category="设计流程">
    ## 设计工作流程
    
    ### Design Sprint设计冲刺
    - **第1天：Map映射**
      - 定义挑战和目标
      - 绘制问题地图
      - 选择焦点领域
      - 邀请专家分享洞察
    
    - **第2天：Sketch草图**
      - 寻找现有解决方案
      - 四步骤草图法
      - 疯狂八分钟
      - 解决方案草图
    
    - **第3天：Decide决策**
      - 热力图投票
      - 速度批评
      - 故事板制作
      - 决定测试方案
    
    - **第4天：Prototype原型**
      - 分工制作原型
      - 快速原型制作
      - 模拟真实体验
      - 准备测试材料
    
    - **第5天：Test测试**
      - 用户测试执行
      - 观察和记录
      - 洞察提取
      - 下一步规划
    
    ### Double Diamond双钻石模型
    - **第一个钻石：发现正确的问题**
      - 发散：广泛收集信息和洞察
      - 聚合：定义核心问题和挑战
    
    - **第二个钻石：找到正确的解决方案**
      - 发散：生成多样化的解决方案
      - 聚合：选择最优的解决方案
    
    ### Agile UX敏捷用户体验
    - **Sprint计划**：将UX工作融入开发冲刺
    - **持续研究**：在开发过程中持续收集用户反馈
    - **快速迭代**：基于反馈快速调整设计
    - **跨职能协作**：UX、产品、开发的紧密合作
  </workflows>
  
  <collaboration category="协作方法">
    ## 团队协作最佳实践
    
    ### 设计系统建设
    - **组件库管理**
      - 原子设计方法
      - 组件文档化
      - 版本控制
      - 使用指南
    
    - **设计规范制定**
      - 视觉规范
      - 交互规范
      - 文案规范
      - 品牌指南
    
    - **工具链集成**
      - 设计工具选择
      - 开发工具集成
      - 版本管理
      - 自动化流程
    
    ### 跨团队协作
    - **需求对齐**
      - 产品需求评审
      - 技术可行性确认
      - 用户需求验证
      - 优先级排序
    
    - **设计评审**
      - 多轮评审机制
      - 利益相关者参与
      - 反馈收集和处理
      - 决策记录
    
    - **交付流程**
      - 设计标注
      - 资源准备
      - 实现验收
      - 上线验证
    
    ### 沟通技巧
    - **设计呈现**
      - 讲故事的方式
      - 视觉化表达
      - 数据支撑
      - 用户视角
    
    - **反馈处理**
      - 积极倾听
      - 建设性回应
      - 方案优化
      - 共识达成
    
    - **冲突解决**
      - 问题识别
      - 多方沟通
      - 妥协方案
      - 决策执行
  </collaboration>
</knowledge> 