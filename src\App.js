import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mail, 
  Settings, 
  Loader2, 
  Check<PERSON><PERSON>cle, 
  AlertTriangle, 
  Copy, 
  Refresh, 
  Trash2,
  Plus,
  Send,
  Clock,
  Zap
} from 'lucide-react';
import toast, { Toaster } from 'react-hot-toast';
import './App.css';

// 主应用组件
function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [emails, setEmails] = useState([]);
  const [emailInput, setEmailInput] = useState('');
  const [apiConfig, setApiConfig] = useState({
    uid: '',
    sign: ''
  });
  const [settings, setSettings] = useState({
    titleFilter: '验证码',
    timeRange: 3,
    extractRule: '$BODYTEXT-R|\\d{6}$',
    mode: 'immediate'
  });
  const [results, setResults] = useState({});

  // 添加邮箱
  const addEmails = () => {
    if (!emailInput.trim()) return;
    
    const newEmails = emailInput
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && !emails.includes(email));
    
    if (newEmails.length > 0) {
      setEmails(prev => [...prev, ...newEmails]);
      setEmailInput('');
      toast.success(`已添加 ${newEmails.length} 个邮箱`);
    }
  };

  // 删除邮箱
  const removeEmail = (emailToRemove) => {
    setEmails(prev => prev.filter(email => email !== emailToRemove));
    setResults(prev => {
      const newResults = { ...prev };
      delete newResults[emailToRemove];
      return newResults;
    });
    toast.success('邮箱已删除');
  };

  // 复制验证码
  const copyCode = (code) => {
    navigator.clipboard.writeText(code);
    toast.success('验证码已复制到剪贴板');
  };

  // 开始验证码获取
  const startVerification = async () => {
    if (!apiConfig.uid || !apiConfig.sign) {
      toast.error('请先配置 API 信息');
      return;
    }
    
    if (emails.length === 0) {
      toast.error('请先添加邮箱地址');
      return;
    }

    setIsLoading(true);
    toast.loading('正在获取验证码...', { id: 'verification' });

    // 模拟API调用 - 在实际应用中这里会调用后端API
    try {
      for (const email of emails) {
        setResults(prev => ({
          ...prev,
          [email]: { status: 'checking', code: null }
        }));
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 模拟结果
        const mockCode = Math.random() > 0.3 ? 
          Math.floor(100000 + Math.random() * 900000).toString() : 
          null;
          
        setResults(prev => ({
          ...prev,
          [email]: { 
            status: mockCode ? 'success' : 'failed', 
            code: mockCode 
          }
        }));
      }
      
      toast.success('验证码获取完成', { id: 'verification' });
    } catch (error) {
      toast.error('获取验证码失败', { id: 'verification' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100">
      <Toaster 
        position="top-right" 
        toastOptions={{
          duration: 4000,
          style: {
            background: '#ffffff',
            color: '#374151',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
          },
        }}
      />
      
      {/* 主容器 */}
      <div className="flex h-screen">
        {/* 左侧边栏 */}
        <motion.div 
          initial={{ x: -300, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          className="w-80 bg-white border-r border-neutral-200 flex flex-col shadow-soft"
        >
          {/* 头部 */}
          <div className="p-6 border-b border-neutral-100">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                <Mail className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-neutral-900">邮箱验证码工具</h1>
                <p className="text-sm text-neutral-500">现代化设计版本</p>
              </div>
            </div>
          </div>

          {/* 配置区域 */}
          <div className="flex-1 overflow-y-auto">
            <ConfigPanel 
              apiConfig={apiConfig}
              setApiConfig={setApiConfig}
              settings={settings}
              setSettings={setSettings}
              emailInput={emailInput}
              setEmailInput={setEmailInput}
              onAddEmails={addEmails}
              emails={emails}
              onRemoveEmail={removeEmail}
            />
          </div>

          {/* 底部操作 */}
          <div className="p-6 border-t border-neutral-100">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={startVerification}
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-4 rounded-xl font-semibold shadow-medium hover:shadow-large transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>处理中...</span>
                </>
              ) : (
                <>
                  {settings.mode === 'immediate' ? (
                    <Zap className="w-5 h-5" />
                  ) : (
                    <Clock className="w-5 h-5" />
                  )}
                  <span>
                    {settings.mode === 'immediate' ? '立即获取' : '智能等待'}
                  </span>
                </>
              )}
            </motion.button>
          </div>
        </motion.div>

        {/* 主内容区域 */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex-1 flex flex-col"
        >
          {/* 顶部状态栏 */}
          <div className="bg-white border-b border-neutral-200 px-8 py-4 shadow-soft">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-xl font-bold text-neutral-900">邮箱状态监控</h2>
                <span className="px-3 py-1 bg-neutral-100 text-neutral-600 rounded-full text-sm font-medium">
                  共 {emails.length} 个邮箱
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-neutral-500">
                  {new Date().toLocaleTimeString('zh-CN')}
                </span>
              </div>
            </div>
          </div>

          {/* 邮箱列表 */}
          <div className="flex-1 p-8">
            <EmailList 
              emails={emails} 
              results={results} 
              onCopyCode={copyCode}
              onRemoveEmail={removeEmail}
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}

// 配置面板组件
function ConfigPanel({ apiConfig, setApiConfig, settings, setSettings, emailInput, setEmailInput, onAddEmails, emails, onRemoveEmail }) {
  return (
    <div className="p-6 space-y-8">
      {/* API 配置 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <h3 className="flex items-center space-x-2 text-lg font-semibold text-neutral-900">
          <Settings className="w-5 h-5" />
          <span>API 配置</span>
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              授权 ID
            </label>
            <input
              type="text"
              value={apiConfig.uid}
              onChange={(e) => setApiConfig(prev => ({ ...prev, uid: e.target.value }))}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="请输入授权 ID"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              API 密钥
            </label>
            <input
              type="password"
              value={apiConfig.sign}
              onChange={(e) => setApiConfig(prev => ({ ...prev, sign: e.target.value }))}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="请输入 API 密钥"
            />
          </div>
        </div>
      </motion.div>

      {/* 邮箱管理 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-4"
      >
        <h3 className="flex items-center space-x-2 text-lg font-semibold text-neutral-900">
          <Mail className="w-5 h-5" />
          <span>邮箱管理</span>
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              邮箱地址 (每行一个)
            </label>
            <textarea
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              rows={3}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
              placeholder="<EMAIL>&#10;<EMAIL>"
            />
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onAddEmails}
            className="w-full bg-success-500 text-white py-2 px-4 rounded-lg font-medium hover:bg-success-600 transition-colors flex items-center justify-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>添加邮箱</span>
          </motion.button>
        </div>

        {/* 当前邮箱列表 */}
        {emails.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-neutral-700">当前邮箱列表:</p>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {emails.map((email, index) => (
                <motion.div
                  key={email}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center justify-between p-2 bg-neutral-50 rounded-lg"
                >
                  <span className="text-sm text-neutral-700 truncate">{email}</span>
                  <button
                    onClick={() => onRemoveEmail(email)}
                    className="text-error-500 hover:text-error-600 p-1"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </motion.div>

      {/* 设置 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <h3 className="text-lg font-semibold text-neutral-900">参数设置</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              邮件标题包含
            </label>
            <input
              type="text"
              value={settings.titleFilter}
              onChange={(e) => setSettings(prev => ({ ...prev, titleFilter: e.target.value }))}
              className="w-full px-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              查询模式
            </label>
            <div className="flex space-x-2">
              <button
                onClick={() => setSettings(prev => ({ ...prev, mode: 'immediate' }))}
                className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                  settings.mode === 'immediate'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                立即查询
              </button>
              <button
                onClick={() => setSettings(prev => ({ ...prev, mode: 'smart' }))}
                className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-colors ${
                  settings.mode === 'smart'
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'
                }`}
              >
                智能等待
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}

// 邮箱列表组件
function EmailList({ emails, results, onCopyCode, onRemoveEmail }) {
  if (emails.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col items-center justify-center h-full text-center"
      >
        <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mb-6">
          <Mail className="w-12 h-12 text-neutral-400" />
        </div>
        <h3 className="text-xl font-semibold text-neutral-900 mb-2">还没有邮箱</h3>
        <p className="text-neutral-500 max-w-md">
          在左侧添加邮箱地址开始使用验证码获取功能
        </p>
      </motion.div>
    );
  }

  return (
    <div className="space-y-4">
      <AnimatePresence>
        {emails.map((email, index) => {
          const result = results[email];
          return (
            <motion.div
              key={email}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className="bg-white rounded-xl p-6 shadow-soft border border-neutral-200 hover:shadow-medium transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center">
                    <Mail className="w-6 h-6 text-primary-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-neutral-900">{email}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <StatusBadge status={result?.status} />
                      {result?.code && (
                        <span className="font-mono text-lg font-bold text-primary-600">
                          {result.code}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {result?.code && (
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => onCopyCode(result.code)}
                      className="p-2 bg-success-100 text-success-600 rounded-lg hover:bg-success-200 transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                    </motion.button>
                  )}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => onRemoveEmail(email)}
                    className="p-2 bg-error-100 text-error-600 rounded-lg hover:bg-error-200 transition-colors"
                  >
                    <Trash2 className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}

// 状态徽章组件
function StatusBadge({ status }) {
  const statusConfig = {
    checking: {
      icon: Loader2,
      text: '检查中',
      className: 'bg-warning-100 text-warning-700',
      animate: true
    },
    success: {
      icon: CheckCircle,
      text: '成功',
      className: 'bg-success-100 text-success-700'
    },
    failed: {
      icon: AlertTriangle,
      text: '失败',
      className: 'bg-error-100 text-error-700'
    }
  };

  if (!status) {
    return (
      <span className="px-2 py-1 bg-neutral-100 text-neutral-600 rounded-full text-xs font-medium">
        等待中
      </span>
    );
  }

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${config.className}`}>
      <Icon className={`w-3 h-3 ${config.animate ? 'animate-spin' : ''}`} />
      <span>{config.text}</span>
    </span>
  );
}

export default App; 