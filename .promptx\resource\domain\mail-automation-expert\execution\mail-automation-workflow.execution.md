<execution>
  <constraint>
    ## 技术限制条件
    - **API限制**：心蓝邮箱助手API调用频率和并发限制
    - **网络依赖**：需要稳定的网络连接进行API调用
    - **邮箱限制**：邮箱服务商的访问频率和安全策略
    - **系统资源**：内存使用和CPU占用需要合理控制
    - **平台兼容**：需要考虑不同操作系统的兼容性
  </constraint>

  <rule>
    ## 强制执行规则
    - **安全第一**：API密钥和邮箱密码必须加密存储
    - **错误处理**：所有API调用必须包含异常处理机制
    - **频率控制**：严格遵守API调用频率限制，避免被封禁
    - **数据验证**：所有用户输入和API返回数据必须验证
    - **日志记录**：关键操作必须记录日志便于调试
  </rule>

  <guideline>
    ## 工作流程指导原则
    - **用户友好**：界面简洁直观，操作步骤清晰
    - **配置灵活**：支持用户自定义提取规则和通知方式
    - **性能优化**：优先使用异步操作，避免界面卡顿
    - **渐进增强**：从基础功能开始，逐步添加高级特性
    - **开源精神**：代码结构清晰，便于社区贡献和维护
  </guideline>

  <process>
    ## 邮箱自动化开发工作流程
    
    ### Phase 1: 需求分析与设计 (30分钟)
    
    ```mermaid
    flowchart TD
        A[用户需求收集] --> B[技术方案设计]
        B --> C[架构设计确认]
        C --> D[开发计划制定]
        
        A1[验证码类型分析] --> A
        A2[使用场景梳理] --> A
        A3[性能要求明确] --> A
        
        B1[技术栈选择] --> B
        B2[API集成方案] --> B
        B3[UI设计方案] --> B
    ```

    #### 关键决策点
    - **编程语言选择**：推荐Python（易用性）或C#（性能）
    - **UI框架选择**：桌面应用推荐Tkinter/PyQt，Web应用推荐Electron
    - **部署方式**：本地运行vs云端部署

    ### Phase 2: 核心功能开发 (2-3小时)
    
    ```mermaid
    graph LR
        A[API客户端] --> B[验证码提取器]
        B --> C[结果处理器]
        C --> D[通知模块]
        
        A1[认证管理] --> A
        A2[请求封装] --> A
        A3[响应解析] --> A
        
        B1[正则匹配] --> B
        B2[内容清洗] --> B
        B3[结果验证] --> B
    ```

    #### 核心模块实现顺序
    1. **配置管理模块**：API密钥、邮箱配置、提取规则
    2. **API客户端模块**：心蓝邮箱API调用封装
    3. **验证码提取模块**：邮件内容解析和验证码提取
    4. **UI交互模块**：用户界面和操作响应
    5. **通知处理模块**：结果展示和通知机制

    ### Phase 3: 功能测试与优化 (1-2小时)
    
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[用户测试]
        C --> D[性能优化]
        D --> E[部署准备]
        
        A1[API调用测试] --> A
        A2[验证码提取测试] --> A
        A3[异常处理测试] --> A
        
        C1[用户体验测试] --> C
        C2[边界情况测试] --> C
        C3[长时间运行测试] --> C
    ```

    ### 详细实施步骤

    #### Step 1: 项目初始化 (5分钟)
    ```bash
    # 创建项目目录
    mkdir mail-verification-automation
    cd mail-verification-automation
    
    # 创建虚拟环境 (Python)
    python -m venv venv
    source venv/bin/activate  # Linux/Mac
    # venv\Scripts\activate     # Windows
    
    # 安装依赖
    pip install requests tkinter configparser cryptography
    ```

    #### Step 2: 配置管理实现 (15分钟)
    ```python
    # config_manager.py
    import configparser
    import os
    from cryptography.fernet import Fernet
    
    class ConfigManager:
        def __init__(self):
            self.config_file = 'config.ini'
            self.key_file = '.key'
            self.cipher = self._load_cipher()
        
        def save_api_config(self, uid, sign, email, password):
            # 加密存储敏感信息
            pass
        
        def load_api_config(self):
            # 解密读取配置
            pass
    ```

    #### Step 3: API客户端实现 (30分钟)
    ```python
    # api_client.py
    import requests
    import time
    import json
    
    class HeartBlueAPIClient:
        def __init__(self, uid, sign, api_url):
            self.uid = uid
            self.sign = sign
            self.api_url = api_url
        
        def check_mail(self, email, title_pattern, from_pattern, fields):
            # 实现邮件检查逻辑
            pass
        
        def get_result(self, task_id):
            # 获取异步任务结果
            pass
    ```

    #### Step 4: 验证码提取器实现 (20分钟)
    ```python
    # code_extractor.py
    import re
    
    class CodeExtractor:
        def __init__(self):
            self.patterns = {
                '6位数字': r'\d{6}',
                '4-8位数字': r'\d{4,8}',
                '字母数字混合': r'[A-Za-z0-9]{6,}'
            }
        
        def extract_code(self, content, pattern_type):
            # 根据规则提取验证码
            pass
    ```

    #### Step 5: UI界面实现 (45分钟)
    ```python
    # main_window.py
    import tkinter as tk
    from tkinter import ttk, messagebox
    
    class MainWindow:
        def __init__(self, root):
            self.root = root
            self.setup_ui()
        
        def setup_ui(self):
            # 创建主界面
            pass
        
        def start_monitoring(self):
            # 开始监控
            pass
    ```

    ### 高级功能扩展

    #### 多邮箱支持
    ```mermaid
    graph TD
        A[邮箱管理器] --> B[邮箱1监控]
        A --> C[邮箱2监控]
        A --> D[邮箱N监控]
        
        B --> E[结果汇总]
        C --> E
        D --> E
        
        E --> F[统一通知]
    ```

    #### 智能规则学习
    ```mermaid
    graph LR
        A[历史数据] --> B[模式识别]
        B --> C[规则优化]
        C --> D[准确率提升]
        D --> E[用户确认]
        E --> A
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 功能完整性
    - ✅ 支持心蓝邮箱API的完整调用流程
    - ✅ 验证码提取准确率 ≥ 95%
    - ✅ 支持常见验证码格式（数字、字母、混合）
    - ✅ 提供用户友好的配置界面

    ### 性能指标
    - ✅ API响应时间 < 5秒
    - ✅ 验证码提取耗时 < 1秒
    - ✅ 界面响应时间 < 500ms
    - ✅ 内存占用 < 100MB

    ### 可靠性要求
    - ✅ 连续运行24小时无崩溃
    - ✅ 网络异常自动恢复
    - ✅ API限制优雅处理
    - ✅ 数据安全加密存储

    ### 用户体验
    - ✅ 安装部署简单（< 5分钟）
    - ✅ 配置操作直观（< 10分钟学会）
    - ✅ 错误信息友好易懂
    - ✅ 支持中文界面和文档

    ### 代码质量
    - ✅ 代码结构清晰模块化
    - ✅ 注释覆盖率 ≥ 80%
    - ✅ 异常处理完善
    - ✅ 符合PEP8编码规范（Python）
  </criteria>
</execution> 