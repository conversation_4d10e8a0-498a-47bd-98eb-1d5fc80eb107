<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>心蓝多邮箱接码 - 启动中</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
        }

        .splash-container {
            text-align: center;
            animation: fadeIn 0.8s ease-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: logoFloat 2s ease-in-out infinite;
        }

        .logo-text {
            font-size: 32px;
            font-weight: bold;
            color: white;
        }

        .app-name {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .app-description {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 40px;
            font-weight: 300;
        }

        .loading-container {
            position: relative;
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 0 auto 20px auto;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,1) 50%, rgba(255,255,255,0.8) 100%);
            border-radius: 2px;
            animation: loading 1.5s ease-in-out infinite;
        }

        .loading-text {
            font-size: 14px;
            opacity: 0.8;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .version {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            opacity: 0.6;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes logoFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes loading {
            0% {
                transform: translateX(-100%);
            }
            50% {
                transform: translateX(0%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.8;
            }
            50% {
                opacity: 1;
            }
        }

        /* 粒子背景效果 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .particle:nth-child(1) {
            width: 6px;
            height: 6px;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            width: 8px;
            height: 8px;
            left: 80%;
            animation-delay: 1.5s;
        }

        .particle:nth-child(3) {
            width: 4px;
            height: 4px;
            left: 60%;
            animation-delay: 3s;
        }

        .particle:nth-child(4) {
            width: 10px;
            height: 10px;
            left: 30%;
            animation-delay: 4.5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.3;
            }
            90% {
                opacity: 0.3;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
    
    <div class="splash-container">
        <div class="logo">
            <div class="logo-text">心</div>
        </div>
        
        <h1 class="app-name">心蓝多邮箱接码</h1>
        <p class="app-description">专业的邮箱验证码管理工具</p>
        
        <div class="loading-container">
            <div class="loading-bar"></div>
        </div>
        
        <p class="loading-text">正在启动应用...</p>
    </div>
    
    <div class="version">v3.0.0</div>
</body>
</html> 