const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const axios = require('axios');
const Store = require('electron-store');
const store = new Store();

// 是否是开发环境
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// 创建主窗口
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 720,
    minWidth: 1000,
    minHeight: 680,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(process.resourcesPath, 'mail_icon.ico'),
    show: false,
    autoHideMenuBar: true,
    backgroundColor: '#F7F9FC'
  });

  // 加载应用
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../../dist/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // 开发环境下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });
}

// 应用准备好后创建窗口
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });

  // 设置IPC通信处理器
  setupIPCHandlers();
});

// 关闭所有窗口时退出应用（macOS除外）
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// 设置IPC通信处理器
function setupIPCHandlers() {
  // 保存配置
  ipcMain.handle('save-config', async (event, config) => {
    try {
      store.set('config', config);
      return { success: true };
    } catch (error) {
      console.error('保存配置失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 加载配置
  ipcMain.handle('load-config', async () => {
    try {
      return { success: true, config: store.get('config') };
    } catch (error) {
      console.error('加载配置失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 检查邮件
  ipcMain.handle('check-mail', async (event, params) => {
    try {
      const { uid, sign, email, title, fields, minutesBack } = params;
      
      const apiUrl = "https://bsh.bhdata.com:30015/bhmailer";
      const requestParams = {
        uid,
        sign,
        act: 'checkMail',
        email,
        title: title || '',
        from: '',
        fields,
        sent: String(Date.now() - minutesBack * 60000),
        t: String(Date.now())
      };

      const response = await axios.get(apiUrl, { params: requestParams });
      return response.data;
    } catch (error) {
      console.error('检查邮件失败:', error);
      return { code: -1, msg: `请求失败: ${error.message}` };
    }
  });
} 