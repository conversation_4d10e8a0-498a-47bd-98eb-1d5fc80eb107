<execution>
  <constraint>
    ## API使用技术限制
    - **频率限制**：API调用必须遵守服务提供商的频率限制
    - **认证限制**：API密钥有效期和权限范围限制
    - **数据限制**：单次请求的数据量和响应大小限制
    - **网络限制**：超时时间、重试次数、并发连接数限制
    - **兼容性限制**：API版本更新和向后兼容性考虑
  </constraint>

  <rule>
    ## API调用强制规则
    - **认证安全**：API密钥必须安全存储，不得硬编码在代码中
    - **错误处理**：每个API调用必须包含完整的异常处理逻辑
    - **重试机制**：网络错误时必须实现指数退避重试策略
    - **数据验证**：API响应数据必须验证格式和完整性
    - **日志记录**：所有API调用必须记录请求和响应日志
  </rule>

  <guideline>
    ## API集成指导原则
    - **防御式编程**：假设所有外部调用都可能失败
    - **渐进式降级**：API不可用时提供备选方案
    - **缓存策略**：合理使用缓存减少API调用次数
    - **监控告警**：实现API调用状态监控和异常告警
    - **文档维护**：维护详细的API使用文档和示例
  </guideline>

  <process>
    ## API最佳实践实施流程

    ### Phase 1: API客户端设计 (45分钟)

    #### 核心客户端架构
    ```mermaid
    graph TD
        A[API客户端] --> B[认证管理器]
        A --> C[请求构建器]
        A --> D[响应解析器]
        A --> E[错误处理器]
        A --> F[重试管理器]
        
        B --> B1[密钥加载]
        B --> B2[签名生成]
        B --> B3[认证刷新]
        
        C --> C1[参数验证]
        C --> C2[URL构建]
        C --> C3[请求封装]
        
        D --> D1[状态检查]
        D --> D2[数据解析]
        D --> D3[结果验证]
    ```

    #### 认证管理最佳实践
    ```python
    class APIAuthManager:
        def __init__(self, config_path):
            self.config = self._load_encrypted_config(config_path)
            self.uid = self.config.get('uid')
            self.sign = self.config.get('sign')
        
        def _load_encrypted_config(self, path):
            """安全加载配置文件"""
            try:
                with open(path, 'rb') as f:
                    encrypted_data = f.read()
                cipher = Fernet(self._get_or_create_key())
                decrypted_data = cipher.decrypt(encrypted_data)
                return json.loads(decrypted_data.decode())
            except Exception as e:
                raise ConfigError(f"配置加载失败: {e}")
        
        def get_auth_params(self):
            """获取认证参数"""
            timestamp = int(time.time() * 1000)
            return {
                'uid': self.uid,
                'sign': self.sign,
                't': timestamp
            }
    ```

    ### Phase 2: 请求处理优化 (30分钟)

    #### 智能重试策略
    ```python
    import time
    import random
    from functools import wraps
    
    def api_retry(max_attempts=3, base_delay=1, max_delay=60):
        """指数退避重试装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except (requests.ConnectionError, requests.Timeout) as e:
                        if attempt == max_attempts - 1:
                            raise APIError(f"API调用失败，已重试{max_attempts}次: {e}")
                        
                        # 指数退避 + 随机抖动
                        delay = min(base_delay * (2 ** attempt), max_delay)
                        jitter = random.uniform(0, 0.1) * delay
                        time.sleep(delay + jitter)
                        
                        logger.warning(f"API调用失败，{delay:.2f}秒后重试 (尝试 {attempt + 1}/{max_attempts})")
                    except requests.HTTPError as e:
                        if e.response.status_code >= 500:
                            # 服务器错误，可以重试
                            continue
                        else:
                            # 客户端错误，不应重试
                            raise APIError(f"API调用错误: {e}")
            return wrapper
        return decorator
    ```

    #### 响应数据验证
    ```python
    from pydantic import BaseModel, validator
    from typing import Optional, Any
    
    class APIResponse(BaseModel):
        code: int
        msg: str
        data: Optional[Any] = None
        
        @validator('code')
        def validate_code(cls, v):
            if v not in [0, 5, -1]:  # 根据心蓝API文档定义
                raise ValueError(f'无效的响应代码: {v}')
            return v
        
        @property
        def is_success(self) -> bool:
            return self.code == 0
        
        @property
        def is_pending(self) -> bool:
            return self.code == 5
        
        @property
        def is_error(self) -> bool:
            return self.code == -1
    ```

    ### Phase 3: 异步处理优化 (30分钟)

    #### 异步任务管理
    ```mermaid
    graph TD
        A[提交任务] --> B[获取任务ID]
        B --> C[轮询状态]
        C --> D{任务状态}
        D -->|进行中| E[等待间隔]
        D -->|完成| F[获取结果]
        D -->|失败| G[错误处理]
        E --> C
        F --> H[返回数据]
        G --> I[重试或放弃]
    ```

    ```python
    import asyncio
    import aiohttp
    from typing import Optional, Dict, Any
    
    class AsyncAPIClient:
        def __init__(self, auth_manager):
            self.auth = auth_manager
            self.session: Optional[aiohttp.ClientSession] = None
        
        async def __aenter__(self):
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'MailAutomationTool/1.0'}
            )
            return self
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            if self.session:
                await self.session.close()
        
        @api_retry()
        async def check_mail_async(self, email: str, title: str, from_addr: str, fields: str) -> Dict[str, Any]:
            """异步检查邮件"""
            params = {
                **self.auth.get_auth_params(),
                'act': 'checkMail',
                'email': email,
                'title': title,
                'from': from_addr,
                'fields': fields,
                'sent': str(int(time.time() * 1000) - 180000)  # 3分钟内
            }
            
            async with self.session.get(self.auth.api_url, params=params) as resp:
                if resp.status != 200:
                    raise APIError(f"HTTP错误: {resp.status}")
                
                data = await resp.json()
                response = APIResponse(**data)
                
                if response.is_pending:
                    # 异步任务，需要轮询结果
                    return await self._poll_result(response.msg)
                elif response.is_success:
                    return response.data
                else:
                    raise APIError(f"API错误: {response.msg}")
        
        async def _poll_result(self, task_id: str, max_wait: int = 300) -> Dict[str, Any]:
            """轮询异步任务结果"""
            start_time = time.time()
            while time.time() - start_time < max_wait:
                params = {
                    **self.auth.get_auth_params(),
                    'act': 'getResult',
                    'id': task_id
                }
                
                async with self.session.get(self.auth.api_url, params=params) as resp:
                    data = await resp.json()
                    response = APIResponse(**data)
                    
                    if response.is_success:
                        return response.data
                    elif response.is_error:
                        raise APIError(f"任务执行失败: {response.msg}")
                    # is_pending，继续等待
                
                await asyncio.sleep(10)  # 10秒间隔轮询
            
            raise APIError(f"任务超时: {task_id}")
    ```

    ### Phase 4: 监控和日志 (20分钟)

    #### API调用监控
    ```python
    import logging
    import time
    from collections import defaultdict, deque
    from dataclasses import dataclass
    from typing import Dict, List
    
    @dataclass
    class APIMetrics:
        total_calls: int = 0
        success_calls: int = 0
        error_calls: int = 0
        avg_response_time: float = 0.0
        last_error: str = ""
        
        @property
        def success_rate(self) -> float:
            return (self.success_calls / self.total_calls * 100) if self.total_calls > 0 else 0
    
    class APIMonitor:
        def __init__(self, window_size: int = 100):
            self.metrics = defaultdict(APIMetrics)
            self.response_times = defaultdict(lambda: deque(maxlen=window_size))
            self.logger = logging.getLogger('api_monitor')
        
        def record_call(self, endpoint: str, success: bool, response_time: float, error: str = ""):
            """记录API调用指标"""
            metrics = self.metrics[endpoint]
            metrics.total_calls += 1
            
            if success:
                metrics.success_calls += 1
            else:
                metrics.error_calls += 1
                metrics.last_error = error
                self.logger.error(f"API调用失败 [{endpoint}]: {error}")
            
            # 更新平均响应时间
            times = self.response_times[endpoint]
            times.append(response_time)
            metrics.avg_response_time = sum(times) / len(times)
            
            # 性能警告
            if response_time > 10:  # 超过10秒
                self.logger.warning(f"API响应缓慢 [{endpoint}]: {response_time:.2f}s")
        
        def get_health_status(self) -> Dict[str, Any]:
            """获取API健康状态"""
            return {
                endpoint: {
                    'success_rate': metrics.success_rate,
                    'avg_response_time': metrics.avg_response_time,
                    'total_calls': metrics.total_calls,
                    'last_error': metrics.last_error
                }
                for endpoint, metrics in self.metrics.items()
            }
    ```

    #### 完整的API客户端实现
    ```python
    class HeartBlueAPIClient:
        def __init__(self, config_path: str):
            self.auth = APIAuthManager(config_path)
            self.monitor = APIMonitor()
            self.logger = logging.getLogger('heartblue_api')
            self.api_url = "https://bsh.bhdata.com:30015/bhmailer"
        
        @api_retry(max_attempts=3)
        def check_mail(self, email: str, title: str, from_addr: str = "", fields: str = "$BODYTEXT-R|\\d{6}$") -> str:
            """检查邮件并提取验证码"""
            start_time = time.time()
            endpoint = "checkMail"
            
            try:
                params = {
                    **self.auth.get_auth_params(),
                    'act': 'checkMail',
                    'email': email,
                    'title': title,
                    'from': from_addr,
                    'fields': fields,
                    'sent': str(int(time.time() * 1000) - 180000)  # 3分钟内的邮件
                }
                
                self.logger.info(f"发起API调用: {endpoint}, email: {email}")
                
                response = requests.get(self.api_url, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                api_response = APIResponse(**data)
                
                if api_response.is_pending:
                    # 需要轮询结果
                    result = self._poll_result(api_response.msg)
                elif api_response.is_success:
                    result = api_response.msg
                else:
                    raise APIError(f"API返回错误: {api_response.msg}")
                
                # 记录成功调用
                response_time = time.time() - start_time
                self.monitor.record_call(endpoint, True, response_time)
                
                self.logger.info(f"API调用成功: {endpoint}, 耗时: {response_time:.2f}s")
                return result
                
            except Exception as e:
                response_time = time.time() - start_time
                self.monitor.record_call(endpoint, False, response_time, str(e))
                raise
        
        def _poll_result(self, task_id: str) -> str:
            """轮询异步任务结果"""
            max_attempts = 30  # 最多等待5分钟 (30 * 10s)
            attempt = 0
            
            while attempt < max_attempts:
                try:
                    params = {
                        **self.auth.get_auth_params(),
                        'act': 'getResult',
                        'id': task_id
                    }
                    
                    response = requests.get(self.api_url, params=params, timeout=30)
                    response.raise_for_status()
                    
                    data = response.json()
                    api_response = APIResponse(**data)
                    
                    if api_response.is_success:
                        return api_response.msg
                    elif api_response.is_error:
                        raise APIError(f"任务执行失败: {api_response.msg}")
                    
                    # 仍在处理中，等待
                    time.sleep(10)
                    attempt += 1
                    
                except Exception as e:
                    self.logger.error(f"轮询结果失败: {e}")
                    if attempt >= max_attempts - 1:
                        raise
                    time.sleep(10)
                    attempt += 1
            
            raise APIError(f"任务超时，ID: {task_id}")
        
        def get_health_status(self) -> Dict[str, Any]:
            """获取API健康状态"""
            return self.monitor.get_health_status()
    ```
  </process>

  <criteria>
    ## API最佳实践质量标准

    ### 可靠性指标
    - ✅ API调用成功率 ≥ 99%
    - ✅ 网络异常自动恢复率 ≥ 95%
    - ✅ 超时和重试机制完善
    - ✅ 错误处理覆盖所有异常场景

    ### 性能指标  
    - ✅ 平均响应时间 < 3秒
    - ✅ 重试延迟策略合理（指数退避）
    - ✅ 并发请求处理能力
    - ✅ 资源使用效率优化

    ### 安全标准
    - ✅ API密钥加密存储
    - ✅ 请求签名验证
    - ✅ 敏感数据传输加密
    - ✅ 访问权限控制

    ### 可维护性
    - ✅ 代码结构清晰模块化
    - ✅ 日志记录详细完整
    - ✅ 监控指标全面
    - ✅ 文档和示例完善

    ### 用户体验
    - ✅ 错误信息友好明确
    - ✅ 进度反馈实时更新
    - ✅ 配置操作简单直观
    - ✅ 异常恢复自动化
  </criteria>
</execution> 