const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 暴露安全的 API 到渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 配置相关
  saveConfig: (config) => ipcRenderer.invoke('save-config', config),
  loadConfig: () => ipcRenderer.invoke('load-config'),
  
  // 邮件检查相关
  checkMail: (params) => ipcRenderer.invoke('check-mail', params),
  
  // 剪贴板相关
  copyToClipboard: (text) => {
    const { clipboard } = require('electron');
    clipboard.writeText(text);
    return true;
  }
}); 