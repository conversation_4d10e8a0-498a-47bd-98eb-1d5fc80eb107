#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化邮箱验证码工具 - Python API 后端
用于与 Electron 前端通信，处理邮箱验证码获取
"""

import os
import sys
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path

# 第三方库
import requests
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
import concurrent.futures

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mail_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# FastAPI 应用实例
app = FastAPI(
    title="现代化邮箱验证码工具 API",
    description="用于获取邮箱验证码的现代化 API 服务",
    version="3.0.0"
)

# CORS 配置 - 允许 Electron 应用访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "file://", "*"],  # 开发环境允许所有源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class APIConfig(BaseModel):
    """API 配置模型"""
    uid: str
    sign: str

class EmailRequest(BaseModel):
    """邮箱验证码请求模型"""
    emails: List[EmailStr]
    title_filter: str = "验证码"
    time_range: int = 3
    extract_rule: str = "$BODYTEXT-R|\\d{6}$"
    mode: str = "immediate"  # immediate 或 smart_wait
    wait_time: int = 120  # 智能等待模式的最大等待时间

class EmailResult(BaseModel):
    """邮箱结果模型"""
    email: str
    status: str  # checking, success, failed
    code: Optional[str] = None
    message: str = ""
    timestamp: str = ""

class BatchResult(BaseModel):
    """批量结果模型"""
    task_id: str
    total: int
    completed: int
    results: List[EmailResult]
    status: str  # running, completed, failed

# 心蓝邮箱助手 API 客户端
class HeartBlueAPIClient:
    """心蓝邮箱助手 API 客户端"""
    
    def __init__(self, uid: str, sign: str):
        self.uid = uid
        self.sign = sign
        self.api_url = "https://bsh.bhdata.com:30015/bhmailer"
        self.session = requests.Session()
        self.session.timeout = 30
    
    async def check_mail_async(self, email: str, title: str = "", extract_rule: str = "$BODYTEXT-R|\\d{6}$", minutes_back: int = 3) -> tuple[int, str]:
        """异步检查邮件并提取验证码"""
        try:
            params = {
                'uid': self.uid,
                'sign': self.sign,
                'act': 'checkMail',
                'email': email,
                'title': title,
                'from': '',
                'fields': extract_rule,
                'sent': str(int(time.time() * 1000) - minutes_back * 60000),
                't': str(int(time.time() * 1000))
            }
            
            # 在线程池中执行同步请求
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None, 
                lambda: self.session.get(self.api_url, params=params)
            )
            
            response.raise_for_status()
            data = response.json()
            
            result_code = data.get('code', -1)
            message = data.get('msg', '未知错误')
            
            logger.info(f"邮箱 {email} 检查结果: code={result_code}, msg={message}")
            return result_code, message
                
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            logger.error(f"邮箱 {email} 检查异常: {error_msg}")
            return -1, error_msg

# 全局变量
api_client: Optional[HeartBlueAPIClient] = None
batch_tasks: Dict[str, BatchResult] = {}

# API 路由
@app.post("/api/config")
async def set_api_config(config: APIConfig):
    """设置 API 配置"""
    global api_client
    try:
        api_client = HeartBlueAPIClient(config.uid, config.sign)
        logger.info(f"API 配置已设置: uid={config.uid}")
        return {"success": True, "message": "API 配置已保存"}
    except Exception as e:
        logger.error(f"设置 API 配置失败: {e}")
        raise HTTPException(status_code=400, detail=f"配置设置失败: {str(e)}")

@app.post("/api/emails/verify", response_model=Dict[str, Any])
async def verify_emails(request: EmailRequest, background_tasks: BackgroundTasks):
    """批量验证邮箱验证码"""
    if not api_client:
        raise HTTPException(status_code=400, detail="请先配置 API 信息")
    
    if not request.emails:
        raise HTTPException(status_code=400, detail="邮箱列表不能为空")
    
    # 生成任务 ID
    task_id = f"task_{int(time.time() * 1000)}"
    
    # 初始化批量结果
    batch_result = BatchResult(
        task_id=task_id,
        total=len(request.emails),
        completed=0,
        results=[],
        status="running"
    )
    batch_tasks[task_id] = batch_result
    
    # 在后台执行验证任务
    background_tasks.add_task(
        process_email_verification,
        task_id,
        request
    )
    
    logger.info(f"开始批量验证任务: {task_id}, 邮箱数量: {len(request.emails)}")
    return {
        "task_id": task_id,
        "message": "验证任务已启动",
        "total": len(request.emails)
    }

@app.get("/api/tasks/{task_id}", response_model=BatchResult)
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in batch_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return batch_tasks[task_id]

@app.get("/api/tasks/{task_id}/results")
async def get_task_results(task_id: str):
    """获取任务结果"""
    if task_id not in batch_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = batch_tasks[task_id]
    return {
        "task_id": task_id,
        "status": task.status,
        "results": [result.dict() for result in task.results]
    }

@app.delete("/api/tasks/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    if task_id in batch_tasks:
        del batch_tasks[task_id]
        logger.info(f"任务已删除: {task_id}")
        return {"message": "任务已删除"}
    else:
        raise HTTPException(status_code=404, detail="任务不存在")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "api_configured": api_client is not None,
        "active_tasks": len(batch_tasks)
    }

# 后台任务函数
async def process_email_verification(task_id: str, request: EmailRequest):
    """处理邮箱验证的后台任务"""
    batch_result = batch_tasks[task_id]
    
    try:
        if request.mode == "immediate":
            # 立即检查模式
            await process_immediate_verification(task_id, request)
        else:
            # 智能等待模式
            await process_smart_wait_verification(task_id, request)
            
        batch_result.status = "completed"
        logger.info(f"任务 {task_id} 已完成")
        
    except Exception as e:
        batch_result.status = "failed"
        logger.error(f"任务 {task_id} 执行失败: {e}")

async def process_immediate_verification(task_id: str, request: EmailRequest):
    """立即检查模式"""
    batch_result = batch_tasks[task_id]
    
    # 使用信号量限制并发数
    semaphore = asyncio.Semaphore(5)  # 最多5个并发请求
    
    async def check_single_email(email: str):
        async with semaphore:
            result = EmailResult(
                email=email,
                status="checking",
                timestamp=datetime.now().isoformat()
            )
            
            try:
                result_code, message = await api_client.check_mail_async(
                    email=email,
                    title=request.title_filter,
                    extract_rule=request.extract_rule,
                    minutes_back=request.time_range
                )
                
                if result_code == 0:
                    result.status = "success"
                    result.code = message
                    result.message = "验证码获取成功"
                elif result_code == 7:
                    result.status = "failed"
                    result.message = "未找到邮件"
                else:
                    result.status = "failed"
                    result.message = message
                    
            except Exception as e:
                result.status = "failed"
                result.message = f"检查异常: {str(e)}"
            
            batch_result.results.append(result)
            batch_result.completed += 1
            
            logger.info(f"邮箱 {email} 检查完成: {result.status}")
    
    # 并发处理所有邮箱
    tasks = [check_single_email(email) for email in request.emails]
    await asyncio.gather(*tasks)

async def process_smart_wait_verification(task_id: str, request: EmailRequest):
    """智能等待模式"""
    batch_result = batch_tasks[task_id]
    
    async def check_email_with_wait(email: str):
        result = EmailResult(
            email=email,
            status="checking",
            timestamp=datetime.now().isoformat()
        )
        
        start_time = time.time()
        attempt = 0
        
        while time.time() - start_time < request.wait_time:
            attempt += 1
            
            try:
                result_code, message = await api_client.check_mail_async(
                    email=email,
                    title=request.title_filter,
                    extract_rule=request.extract_rule,
                    minutes_back=request.time_range
                )
                
                # 如果找到验证码，立即返回
                if result_code == 0:
                    result.status = "success"
                    result.code = message
                    result.message = f"验证码获取成功 (尝试 {attempt} 次)"
                    break
                
                # 等待10秒再次尝试
                if time.time() - start_time < request.wait_time - 10:
                    await asyncio.sleep(10)
                else:
                    result.status = "failed"
                    result.message = f"智能等待超时 (尝试了 {attempt} 次)"
                    break
                    
            except Exception as e:
                if time.time() - start_time >= request.wait_time - 10:
                    result.status = "failed"
                    result.message = f"检查异常: {str(e)}"
                    break
                await asyncio.sleep(10)
        
        batch_result.results.append(result)
        batch_result.completed += 1
        
        logger.info(f"邮箱 {email} 智能等待完成: {result.status}")
    
    # 使用信号量限制并发数
    semaphore = asyncio.Semaphore(3)  # 智能等待模式用更少的并发
    
    async def limited_check(email: str):
        async with semaphore:
            await check_email_with_wait(email)
    
    # 并发处理所有邮箱
    tasks = [limited_check(email) for email in request.emails]
    await asyncio.gather(*tasks)

# 启动服务器
def start_server():
    """启动 API 服务器"""
    port = int(os.environ.get("PORT", 8000))
    host = os.environ.get("HOST", "localhost")
    
    logger.info(f"启动邮箱验证码 API 服务器: http://{host}:{port}")
    
    uvicorn.run(
        app,
        host=host,
        port=port,
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    start_server() 