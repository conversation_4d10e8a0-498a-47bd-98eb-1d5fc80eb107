@echo off
chcp 65001 >nul
echo 🚀 现代化邮箱验证码工具 - 开发环境启动
echo.

:: 检查 Node.js
where node >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误: 未找到 Node.js，请先安装 Node.js
    pause
    exit /b 1
)

:: 检查 Python
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ 错误: 未找到 Python，请先安装 Python 3.8+
    pause
    exit /b 1
)

:: 安装前端依赖
echo 📦 安装前端依赖...
if not exist node_modules (
    call npm install
    if %ERRORLEVEL% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)

:: 安装后端依赖
echo 📦 安装后端依赖...
if not exist python-api\venv (
    cd python-api
    python -m venv venv
    call venv\Scripts\activate
    pip install fastapi uvicorn requests pydantic[email] python-multipart
    cd ..
)

:: 启动后端服务
echo 🖥️ 启动后端 API 服务...
start cmd /k "cd python-api && venv\Scripts\activate && python main.py"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端开发服务器
echo 🌐 启动前端开发服务器...
start cmd /k "npm run electron-dev"

echo.
echo ✅ 开发环境启动完成！
echo 📱 前端地址: http://localhost:3000
echo 🔌 后端 API: http://localhost:8000
echo 📚 API 文档: http://localhost:8000/docs
echo.
echo 🔧 开发提示:
echo - 前端代码修改会自动热重载
echo - 后端代码修改需要重启 Python 服务
echo - 使用 Ctrl+C 停止服务
echo.
pause 