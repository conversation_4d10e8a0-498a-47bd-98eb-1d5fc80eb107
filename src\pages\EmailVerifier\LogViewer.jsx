import React, { useEffect, useRef } from 'react';

const LogViewer = ({ logs }) => {
  const logEndRef = useRef(null);

  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);

  return (
    <div className="h-full overflow-auto bg-gray-900 text-gray-200 p-2">
      {logs.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <p className="text-sm text-gray-400">暂无日志记录</p>
        </div>
      ) : (
        <div className="space-y-1">
          {logs.map((entry, index) => (
            <div key={index} className="text-xs font-mono">
              {entry}
            </div>
          ))}
          <div ref={logEndRef} />
        </div>
      )}
    </div>
  );
};

export default LogViewer; 