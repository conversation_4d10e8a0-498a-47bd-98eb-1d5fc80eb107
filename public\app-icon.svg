<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="mailGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="codeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="8" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#mailGradient)" filter="url(#shadow)"/>
  
  <!-- 主邮件图标 -->
  <g transform="translate(128, 128)">
    <!-- 邮件本体 -->
    <rect x="-45" y="-25" width="90" height="60" rx="8" ry="8" 
          fill="white" opacity="0.95" filter="url(#shadow)"/>
    
    <!-- 邮件信封线条 -->
    <path d="M -45 -25 L 0 10 L 45 -25" 
          stroke="#3B82F6" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- 验证码盾牌 -->
    <g transform="translate(25, -35)">
      <path d="M 0 -12 L 12 -8 L 12 8 C 12 12 8 16 0 20 C -8 16 -12 12 -12 8 L -12 -8 Z" 
            fill="url(#shieldGradient)" filter="url(#glow)"/>
      
      <!-- 盾牌上的对勾 -->
      <path d="M -6 0 L -2 4 L 6 -4" 
            stroke="white" stroke-width="2.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    
    <!-- 验证码数字 -->
    <g transform="translate(0, 50)">
      <!-- 验证码背景 -->
      <rect x="-25" y="-8" width="50" height="16" rx="8" ry="8" 
            fill="url(#codeGradient)" opacity="0.9"/>
      
      <!-- 验证码文字 -->
      <text x="0" y="2" text-anchor="middle" 
            font-family="Monaco, 'Courier New', monospace" 
            font-size="11" font-weight="bold" fill="white">123456</text>
    </g>
    
    <!-- 装饰性光点 -->
    <circle cx="-35" cy="-40" r="3" fill="white" opacity="0.6"/>
    <circle cx="40" cy="25" r="2" fill="white" opacity="0.4"/>
    <circle cx="-25" cy="40" r="2.5" fill="white" opacity="0.5"/>
    
    <!-- 邮件内容线条 -->
    <g opacity="0.6">
      <rect x="-35" y="-15" width="25" height="2" rx="1" fill="#3B82F6"/>
      <rect x="-35" y="-8" width="35" height="2" rx="1" fill="#3B82F6"/>
      <rect x="-35" y="-1" width="20" height="2" rx="1" fill="#3B82F6"/>
    </g>
  </g>
  
  <!-- 外圈装饰 -->
  <circle cx="128" cy="128" r="115" fill="none" stroke="white" stroke-width="2" opacity="0.3"/>
</svg> 