const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 安全地暴露 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getVersion: () => ipcRenderer.invoke('get-app-version'),
  getAppPath: () => ipcRenderer.invoke('get-app-path'),
  
  // 平台信息
  platform: process.platform,
  
  // 窗口控制
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),
  
  // 通知相关
  showNotification: (title, body) => {
    new Notification(title, { body });
  },
  
  // 文件系统相关 (如需要)
  selectFile: () => ipcRenderer.invoke('dialog:openFile'),
  
  // 其他 IPC 通信
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  send: (channel, ...args) => ipcRenderer.send(channel, ...args),
  
  // 监听器
  on: (channel, callback) => {
    ipcRenderer.on(channel, callback);
  },
  
  removeListener: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  }
});

// 为了兼容性，也暴露为window.electron
contextBridge.exposeInMainWorld('electron', {
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close')
}); 