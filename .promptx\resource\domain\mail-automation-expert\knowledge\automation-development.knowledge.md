# 自动化开发专业知识体系

## 自动化开发核心理念

### 自动化价值模型
```mermaid
graph TD
    A[手动操作] --> B[效率问题]
    A --> C[错误风险]
    A --> D[重复劳动]
    
    E[自动化解决方案] --> F[效率提升]
    E --> G[准确性保证]
    E --> H[资源释放]
    
    B --> E
    C --> E
    D --> E
```

### 自动化成熟度模型
1. **手动阶段**：完全依赖人工操作
2. **工具辅助**：提供工具简化操作
3. **半自动化**：关键步骤自动，需人工确认
4. **全自动化**：端到端自动运行
5. **智能自动化**：自适应和自学习能力

## 技术栈选择指南

### 编程语言对比

#### Python（推荐指数：⭐⭐⭐⭐⭐）
**优势**：
- 丰富的HTTP库（requests, aiohttp）
- 强大的正则表达式支持
- 简洁的语法，开发效率高
- 庞大的生态系统
- 跨平台兼容性好

**适用场景**：
- 快速原型开发
- API集成和数据处理
- 桌面应用（Tkinter, PyQt）
- 脚本自动化

**示例项目结构**：
```
mail-automation/
├── src/
│   ├── api/
│   │   ├── __init__.py
│   │   ├── client.py
│   │   └── auth.py
│   ├── ui/
│   │   ├── __init__.py
│   │   ├── main_window.py
│   │   └── config_dialog.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── extractor.py
│   │   └── monitor.py
│   └── utils/
│       ├── __init__.py
│       ├── config.py
│       └── logger.py
├── tests/
├── requirements.txt
├── setup.py
└── main.py
```

#### C#（推荐指数：⭐⭐⭐⭐）
**优势**：
- 强类型系统，代码可靠性高
- 优秀的Windows平台集成
- 丰富的UI框架（WinForms, WPF）
- 良好的性能表现
- Visual Studio强大IDE支持

**适用场景**：
- Windows桌面应用
- 企业级应用开发
- 性能要求较高的场景

#### JavaScript/Node.js（推荐指数：⭐⭐⭐）
**优势**：
- Web技术栈统一
- Electron跨平台桌面应用
- 异步处理天然优势
- 丰富的NPM生态

**适用场景**：
- 跨平台桌面应用
- Web应用开发
- 前端开发者友好

### UI框架选择

#### 桌面应用框架对比

| 框架 | 语言 | 跨平台 | 学习曲线 | 性能 | 推荐场景 |
|------|------|--------|----------|------|----------|
| Tkinter | Python | ✅ | 简单 | 中等 | 快速原型 |
| PyQt/PySide | Python | ✅ | 中等 | 高 | 专业应用 |
| WinForms | C# | ❌ | 简单 | 高 | Windows应用 |
| WPF | C# | ❌ | 复杂 | 高 | 现代Windows应用 |
| Electron | JS | ✅ | 中等 | 中等 | Web技术栈 |

#### Tkinter示例界面设计
```python
import tkinter as tk
from tkinter import ttk, messagebox
import threading

class MailAutomationUI:
    def __init__(self, root):
        self.root = root
        self.root.title("邮箱验证码自动化工具")
        self.root.geometry("600x500")
        
        self.setup_ui()
        self.setup_bindings()
    
    def setup_ui(self):
        # 配置区域
        config_frame = ttk.LabelFrame(self.root, text="配置信息", padding=10)
        config_frame.pack(fill="x", padx=10, pady=5)
        
        # API配置
        ttk.Label(config_frame, text="授权ID:").grid(row=0, column=0, sticky="w", pady=2)
        self.uid_entry = ttk.Entry(config_frame, width=30)
        self.uid_entry.grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(config_frame, text="API密钥:").grid(row=1, column=0, sticky="w", pady=2)
        self.sign_entry = ttk.Entry(config_frame, width=30, show="*")
        self.sign_entry.grid(row=1, column=1, padx=5, pady=2)
        
        # 邮箱配置
        ttk.Label(config_frame, text="邮箱地址:").grid(row=2, column=0, sticky="w", pady=2)
        self.email_entry = ttk.Entry(config_frame, width=30)
        self.email_entry.grid(row=2, column=1, padx=5, pady=2)
        
        # 提取规则
        ttk.Label(config_frame, text="提取规则:").grid(row=3, column=0, sticky="w", pady=2)
        self.pattern_combo = ttk.Combobox(config_frame, width=27, values=[
            "6位数字: $BODYTEXT-R|\\d{6}$",
            "4-8位数字: $BODYTEXT-R|\\d{4,8}$",
            "字母数字混合: $BODYTEXT-R|[A-Za-z0-9]{6,}$"
        ])
        self.pattern_combo.grid(row=3, column=1, padx=5, pady=2)
        self.pattern_combo.current(0)
        
        # 控制区域
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill="x", padx=10, pady=5)
        
        self.start_btn = ttk.Button(control_frame, text="开始监控", command=self.start_monitoring)
        self.start_btn.pack(side="left", padx=5)
        
        self.stop_btn = ttk.Button(control_frame, text="停止监控", command=self.stop_monitoring, state="disabled")
        self.stop_btn.pack(side="left", padx=5)
        
        self.test_btn = ttk.Button(control_frame, text="测试连接", command=self.test_connection)
        self.test_btn.pack(side="left", padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(self.root, text="监控结果", padding=10)
        result_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 结果文本框
        self.result_text = tk.Text(result_frame, height=15, wrap="word")
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief="sunken")
        status_bar.pack(side="bottom", fill="x")
```

## 并发和异步处理

### 异步编程模式

#### Python asyncio示例
```python
import asyncio
import aiohttp
import time
from typing import List, Dict, Any

class AsyncMailMonitor:
    def __init__(self, api_client):
        self.api_client = api_client
        self.monitoring = False
        self.tasks = []
    
    async def monitor_multiple_emails(self, email_configs: List[Dict[str, Any]]):
        """并发监控多个邮箱"""
        self.monitoring = True
        tasks = []
        
        for config in email_configs:
            task = asyncio.create_task(
                self.monitor_single_email(config)
            )
            tasks.append(task)
            
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            print(f"监控过程中出现错误: {e}")
        finally:
            self.monitoring = False
    
    async def monitor_single_email(self, config: Dict[str, Any]):
        """监控单个邮箱"""
        email = config['email']
        check_interval = config.get('interval', 30)  # 默认30秒
        
        while self.monitoring:
            try:
                result = await self.api_client.check_mail_async(
                    email=email,
                    title=config.get('title', ''),
                    fields=config.get('fields', '$BODYTEXT-R|\\d{6}$')
                )
                
                if result:
                    await self.handle_result(email, result)
                    
            except Exception as e:
                print(f"检查邮箱 {email} 时出错: {e}")
            
            await asyncio.sleep(check_interval)
    
    async def handle_result(self, email: str, result: str):
        """处理检测结果"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {email}: 发现验证码 - {result}")
        
        # 可以在这里添加通知逻辑
        await self.send_notification(email, result)
    
    async def send_notification(self, email: str, code: str):
        """发送通知"""
        # 实现通知逻辑：桌面通知、声音提醒等
        pass
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
```

#### 线程池处理
```python
import concurrent.futures
import threading
from queue import Queue

class ThreadPoolMailMonitor:
    def __init__(self, max_workers=5):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.result_queue = Queue()
        self.monitoring = False
    
    def start_monitoring(self, email_configs):
        """启动多线程监控"""
        self.monitoring = True
        
        futures = []
        for config in email_configs:
            future = self.executor.submit(self.monitor_email_sync, config)
            futures.append(future)
        
        # 启动结果处理线程
        result_thread = threading.Thread(target=self.process_results)
        result_thread.daemon = True
        result_thread.start()
        
        return futures
    
    def monitor_email_sync(self, config):
        """同步方式监控邮箱"""
        email = config['email']
        while self.monitoring:
            try:
                result = self.api_client.check_mail(
                    email=email,
                    title=config.get('title', ''),
                    fields=config.get('fields', '$BODYTEXT-R|\\d{6}$')
                )
                
                if result:
                    self.result_queue.put((email, result))
                    
            except Exception as e:
                print(f"监控邮箱 {email} 出错: {e}")
            
            time.sleep(config.get('interval', 30))
    
    def process_results(self):
        """处理监控结果"""
        while self.monitoring:
            try:
                email, result = self.result_queue.get(timeout=1)
                self.handle_result(email, result)
            except:
                continue
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.executor.shutdown(wait=True)
```

## 配置管理和数据持久化

### 配置文件设计
```python
import json
import configparser
from cryptography.fernet import Fernet
from pathlib import Path
import os

class ConfigManager:
    def __init__(self, config_dir="config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / "app.ini"
        self.secure_file = self.config_dir / "secure.dat"
        self.key_file = self.config_dir / ".key"
        
        self.config = configparser.ConfigParser()
        self.cipher = self._get_or_create_cipher()
    
    def _get_or_create_cipher(self):
        """获取或创建加密密钥"""
        if self.key_file.exists():
            with open(self.key_file, 'rb') as f:
                key = f.read()
        else:
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
            # 设置文件为只读
            os.chmod(self.key_file, 0o600)
        
        return Fernet(key)
    
    def save_basic_config(self, **kwargs):
        """保存基础配置"""
        if 'general' not in self.config:
            self.config.add_section('general')
        
        for key, value in kwargs.items():
            self.config.set('general', key, str(value))
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def save_secure_config(self, uid, sign, emails):
        """保存敏感配置（加密）"""
        secure_data = {
            'uid': uid,
            'sign': sign,
            'emails': emails
        }
        
        json_data = json.dumps(secure_data).encode()
        encrypted_data = self.cipher.encrypt(json_data)
        
        with open(self.secure_file, 'wb') as f:
            f.write(encrypted_data)
    
    def load_secure_config(self):
        """加载敏感配置（解密）"""
        if not self.secure_file.exists():
            return {}
        
        try:
            with open(self.secure_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self.cipher.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
        except Exception as e:
            print(f"配置解密失败: {e}")
            return {}
    
    def load_basic_config(self):
        """加载基础配置"""
        if self.config_file.exists():
            self.config.read(self.config_file, encoding='utf-8')
        
        return dict(self.config.items('general')) if 'general' in self.config else {}
```

### 数据库存储方案
```python
import sqlite3
import json
from datetime import datetime
from contextlib import contextmanager

class DataManager:
    def __init__(self, db_path="data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        with self.get_connection() as conn:
            conn.executescript("""
                CREATE TABLE IF NOT EXISTS verification_codes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    code TEXT NOT NULL,
                    source_title TEXT,
                    source_from TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    used BOOLEAN DEFAULT FALSE
                );
                
                CREATE TABLE IF NOT EXISTS monitoring_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    status TEXT NOT NULL,
                    message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS email_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE NOT NULL,
                    config_json TEXT NOT NULL,
                    active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_verification_codes_email 
                ON verification_codes(email);
                
                CREATE INDEX IF NOT EXISTS idx_monitoring_logs_email 
                ON monitoring_logs(email);
            """)
    
    @contextmanager
    def get_connection(self):
        """数据库连接上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def save_verification_code(self, email, code, title="", from_addr=""):
        """保存验证码"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO verification_codes (email, code, source_title, source_from)
                VALUES (?, ?, ?, ?)
            """, (email, code, title, from_addr))
    
    def get_latest_code(self, email, limit=1):
        """获取最新验证码"""
        with self.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM verification_codes 
                WHERE email = ? AND used = FALSE
                ORDER BY created_at DESC 
                LIMIT ?
            """, (email, limit))
            return cursor.fetchall()
    
    def mark_code_used(self, code_id):
        """标记验证码已使用"""
        with self.get_connection() as conn:
            conn.execute("""
                UPDATE verification_codes 
                SET used = TRUE 
                WHERE id = ?
            """, (code_id,))
    
    def log_monitoring_activity(self, email, status, message=""):
        """记录监控活动"""
        with self.get_connection() as conn:
            conn.execute("""
                INSERT INTO monitoring_logs (email, status, message)
                VALUES (?, ?, ?)
            """, (email, status, message))
    
    def get_monitoring_stats(self, email=None, days=7):
        """获取监控统计"""
        with self.get_connection() as conn:
            if email:
                cursor = conn.execute("""
                    SELECT status, COUNT(*) as count
                    FROM monitoring_logs 
                    WHERE email = ? AND created_at > datetime('now', '-{} days')
                    GROUP BY status
                """.format(days), (email,))
            else:
                cursor = conn.execute("""
                    SELECT email, status, COUNT(*) as count
                    FROM monitoring_logs 
                    WHERE created_at > datetime('now', '-{} days')
                    GROUP BY email, status
                """.format(days))
            
            return cursor.fetchall()
```

## 错误处理和日志系统

### 分层错误处理
```python
import logging
import traceback
from enum import Enum
from dataclasses import dataclass
from typing import Optional, Any

class ErrorLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class AppError:
    level: ErrorLevel
    code: str
    message: str
    details: Optional[str] = None
    original_error: Optional[Exception] = None

class ErrorHandler:
    def __init__(self, logger):
        self.logger = logger
        self.error_callbacks = {}
    
    def register_callback(self, error_code: str, callback):
        """注册错误处理回调"""
        self.error_callbacks[error_code] = callback
    
    def handle_error(self, error: AppError) -> bool:
        """处理错误"""
        # 记录日志
        log_message = f"[{error.code}] {error.message}"
        if error.details:
            log_message += f" - {error.details}"
        
        if error.level == ErrorLevel.INFO:
            self.logger.info(log_message)
        elif error.level == ErrorLevel.WARNING:
            self.logger.warning(log_message)
        elif error.level == ErrorLevel.ERROR:
            self.logger.error(log_message)
        elif error.level == ErrorLevel.CRITICAL:
            self.logger.critical(log_message)
        
        # 记录异常堆栈
        if error.original_error:
            self.logger.debug(traceback.format_exc())
        
        # 执行回调
        if error.code in self.error_callbacks:
            try:
                return self.error_callbacks[error.code](error)
            except Exception as e:
                self.logger.error(f"错误回调执行失败: {e}")
        
        return False

class LoggerManager:
    @staticmethod
    def setup_logger(name="mail_automation", log_file="app.log", level=logging.INFO):
        """设置日志系统"""
        logger = logging.getLogger(name)
        logger.setLevel(level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger

# 使用示例
class MailAutomationApp:
    def __init__(self):
        self.logger = LoggerManager.setup_logger()
        self.error_handler = ErrorHandler(self.logger)
        self.setup_error_handlers()
    
    def setup_error_handlers(self):
        """设置错误处理器"""
        self.error_handler.register_callback("API_AUTH_FAILED", self.handle_auth_error)
        self.error_handler.register_callback("NETWORK_ERROR", self.handle_network_error)
        self.error_handler.register_callback("CONFIG_ERROR", self.handle_config_error)
    
    def handle_auth_error(self, error: AppError) -> bool:
        """处理认证错误"""
        # 弹出配置对话框
        self.show_config_dialog("API认证失败，请检查授权ID和密钥")
        return True
    
    def handle_network_error(self, error: AppError) -> bool:
        """处理网络错误"""
        # 显示重试选项
        return self.show_retry_dialog("网络连接失败，是否重试？")
    
    def handle_config_error(self, error: AppError) -> bool:
        """处理配置错误"""
        # 重置配置文件
        self.reset_config()
        return True
```

## 性能优化策略

### 内存管理
```python
import gc
import psutil
import time
from functools import wraps

class PerformanceMonitor:
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = time.time()
    
    def get_memory_usage(self):
        """获取内存使用情况"""
        memory_info = self.process.memory_info()
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': self.process.memory_percent()
        }
    
    def get_cpu_usage(self):
        """获取CPU使用率"""
        return self.process.cpu_percent(interval=1)
    
    def force_gc(self):
        """强制垃圾回收"""
        collected = gc.collect()
        return collected

def memory_limit(max_mb=100):
    """内存使用限制装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            monitor = PerformanceMonitor()
            
            result = func(*args, **kwargs)
            
            memory = monitor.get_memory_usage()
            if memory['rss'] > max_mb:
                print(f"警告：内存使用超限 {memory['rss']:.2f}MB > {max_mb}MB")
                monitor.force_gc()
            
            return result
        return wrapper
    return decorator

# 缓存策略
from functools import lru_cache
import time

class TimedCache:
    def __init__(self, timeout=300):  # 5分钟缓存
        self.cache = {}
        self.timeout = timeout
    
    def get(self, key):
        if key in self.cache:
            value, timestamp = self.cache[key]
            if time.time() - timestamp < self.timeout:
                return value
            else:
                del self.cache[key]
        return None
    
    def set(self, key, value):
        self.cache[key] = (value, time.time())
    
    def clear_expired(self):
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self.cache.items()
            if current_time - timestamp >= self.timeout
        ]
        for key in expired_keys:
            del self.cache[key]
```

## 测试和质量保证

### 单元测试框架
```python
import unittest
import mock
from unittest.mock import patch, MagicMock
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from api.client import HeartBlueAPIClient
from core.extractor import CodeExtractor

class TestHeartBlueAPIClient(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        self.config_path = "test_config.json"
        self.client = HeartBlueAPIClient(self.config_path)
    
    @patch('requests.get')
    def test_check_mail_success(self, mock_get):
        """测试成功获取邮件"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "msg": "123456"
        }
        mock_get.return_value = mock_response
        
        result = self.client.check_mail(
            email="<EMAIL>",
            title="验证码",
            fields="$BODYTEXT-R|\\d{6}$"
        )
        
        self.assertEqual(result, "123456")
    
    @patch('requests.get')
    def test_check_mail_pending(self, mock_get):
        """测试异步任务处理"""
        # 模拟异步任务响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 5,
            "msg": "task_id_123"
        }
        mock_get.return_value = mock_response
        
        # 模拟轮询结果
        with patch.object(self.client, '_poll_result') as mock_poll:
            mock_poll.return_value = "654321"
            
            result = self.client.check_mail(
                email="<EMAIL>",
                title="验证码"
            )
            
            self.assertEqual(result, "654321")
            mock_poll.assert_called_once_with("task_id_123")
    
    def test_invalid_config(self):
        """测试无效配置"""
        with self.assertRaises(Exception):
            client = HeartBlueAPIClient("nonexistent_config.json")

class TestCodeExtractor(unittest.TestCase):
    def setUp(self):
        self.extractor = CodeExtractor()
    
    def test_extract_6_digit_code(self):
        """测试提取6位数字验证码"""
        content = "您的验证码是：123456，请在10分钟内使用。"
        pattern = r'\d{6}'
        
        result = self.extractor.extract_code(content, pattern)
        self.assertEqual(result, "123456")
    
    def test_extract_complex_code(self):
        """测试提取复杂格式验证码"""
        content = "Your verification code: ABC123, valid for 5 minutes."
        pattern = r'[A-Z]{3}\d{3}'
        
        result = self.extractor.extract_code(content, pattern)
        self.assertEqual(result, "ABC123")
    
    def test_no_match(self):
        """测试无匹配内容"""
        content = "这是一段没有验证码的文本。"
        pattern = r'\d{6}'
        
        result = self.extractor.extract_code(content, pattern)
        self.assertIsNone(result)

if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
```

### 集成测试
```python
import pytest
import time
import threading
from unittest.mock import patch

class TestMailAutomationIntegration:
    @pytest.fixture
    def app_instance(self):
        """创建应用实例"""
        from main import MailAutomationApp
        app = MailAutomationApp()
        yield app
        app.cleanup()
    
    def test_end_to_end_workflow(self, app_instance):
        """端到端工作流测试"""
        # 配置邮箱
        config = {
            'email': '<EMAIL>',
            'title': '验证码',
            'fields': '$BODYTEXT-R|\\d{6}$',
            'interval': 5
        }
        
        # 启动监控
        app_instance.add_email_config(config)
        
        # 模拟收到邮件
        with patch.object(app_instance.api_client, 'check_mail') as mock_check:
            mock_check.return_value = "123456"
            
            # 启动监控线程
            monitor_thread = threading.Thread(
                target=app_instance.start_monitoring
            )
            monitor_thread.start()
            
            # 等待检测
            time.sleep(6)
            
            # 停止监控
            app_instance.stop_monitoring()
            monitor_thread.join(timeout=10)
            
            # 验证结果
            codes = app_instance.data_manager.get_latest_code('<EMAIL>')
            assert len(codes) > 0
            assert codes[0]['code'] == "123456"
    
    def test_error_recovery(self, app_instance):
        """错误恢复测试"""
        # 模拟网络错误
        with patch.object(app_instance.api_client, 'check_mail') as mock_check:
            mock_check.side_effect = [
                Exception("Network error"),  # 第一次失败
                Exception("Network error"),  # 第二次失败
                "123456"  # 第三次成功
            ]
            
            result = app_instance.check_with_retry('<EMAIL>')
            assert result == "123456"
```

## 部署和分发策略

### 打包配置
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="mail-automation-tool",
    version="1.0.0",
    description="基于心蓝邮箱助手API的验证码自动化工具",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=[
        "requests>=2.25.0",
        "aiohttp>=3.8.0",
        "cryptography>=3.4.0",
        "pydantic>=1.8.0",
        "psutil>=5.8.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0.0",
            "pytest-asyncio>=0.18.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
        ],
        "gui": [
            "PyQt5>=5.15.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "mail-automation=main:main",
        ],
    },
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
```

### Docker化部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY main.py .

# 创建数据目录
RUN mkdir -p /app/data /app/config /app/logs

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV DATA_DIR=/app/data
ENV CONFIG_DIR=/app/config
ENV LOG_DIR=/app/logs

# 暴露端口（如果有Web界面）
EXPOSE 8080

# 启动命令
CMD ["python", "main.py"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  mail-automation:
    build: .
    container_name: mail-automation
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    networks:
      - mail-net

networks:
  mail-net:
    driver: bridge
```

这个知识体系涵盖了自动化开发的各个方面，从技术选择到部署策略，为开发邮箱验证码自动化工具提供了全面的指导。 