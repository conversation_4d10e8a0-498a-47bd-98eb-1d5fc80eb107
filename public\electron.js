const { app, BrowserWindow, ipcMain, shell, Menu } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev') || process.env.NODE_ENV === 'development';

// 保持对window对象的全局引用，避免被垃圾回收
let mainWindow;
let splashWindow;

function createSplashWindow() {
  // 创建启动界面窗口
  splashWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: false,
    resizable: false,
    backgroundColor: '#667eea',
    center: true,
    show: true,
    skipTaskbar: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true
    }
  });

  // 加载启动界面
  splashWindow.loadFile(path.join(__dirname, 'splash.html'));

  // 禁用菜单
  splashWindow.setMenu(null);

  // 启动界面窗口关闭事件
  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

function createWindow() {
  // 创建浏览器窗口 - 简化策略，确保稳定性
  mainWindow = new BrowserWindow({
    width: 1100,        // 略微增加宽度
    height: 750,        // 增加高度，确保左侧配置完全可见
    minWidth: 1000,     // 最小宽度
    minHeight: 700,     // 最小高度
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      backgroundThrottling: false,  // 禁用后台节流
      offscreen: false
    },
    show: false,        // 完全隐藏直到准备好
    frame: false,       // 隐藏窗口外框
    titleBarStyle: 'hidden',
    backgroundColor: '#f1f5f9',  // 精确匹配应用背景色
    icon: path.join(__dirname, 'app-icon.svg'),
    transparent: false,  // 使用实体窗口
    vibrancy: false,     // 禁用所有视觉效果
    paintWhenInitiallyHidden: true,
    skipTaskbar: false,
    center: true        // 窗口居中显示
  });

  // 隐藏菜单栏
  Menu.setApplicationMenu(null);

  // 加载应用
  const startUrl = isDev ? 'http://localhost:3000' : `file://${path.join(__dirname, '../build/index.html')}`;
  mainWindow.loadURL(startUrl);

  // 等待主应用加载完成后显示并关闭启动界面
  function showMainWindow() {
    if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.show();
      mainWindow.focus();
      
      // 关闭启动界面
      if (splashWindow && !splashWindow.isDestroyed()) {
        splashWindow.close();
      }
    }
  }
  
  // 监听页面加载完成
  mainWindow.webContents.once('did-finish-load', () => {
    // 确保React组件完全渲染后再显示
    setTimeout(showMainWindow, 300);
  });

  // DOM准备好后的备用机制
  mainWindow.webContents.once('dom-ready', () => {
    setTimeout(showMainWindow, 500);
  });

  // 最终保险机制
  setTimeout(showMainWindow, 3000);

  // 开发模式下打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // 当窗口关闭时触发
  mainWindow.on('closed', () => {
    mainWindow = null;
    // 如果启动界面还在，也关闭它
    if (splashWindow && !splashWindow.isDestroyed()) {
      splashWindow.close();
    }
  });

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用
app.whenReady().then(() => {
  // 首先显示启动界面
  createSplashWindow();
  
  // 延迟一小段时间后开始创建主窗口
  setTimeout(() => {
    createWindow();
  }, 100);
});

// 当所有窗口关闭时退出应用 (macOS除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在 macOS 上，点击 dock 图标且没有其他窗口打开时，
  // 通常会重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC 处理程序 - 与渲染进程通信
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-app-path', () => {
  return app.getAppPath();
});

// 窗口控制处理程序
ipcMain.handle('window-minimize', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.restore();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window-close', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// 应用安全配置
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (navigationEvent, navigationUrl) => {
    navigationEvent.preventDefault();
    shell.openExternal(navigationUrl);
  });
}); 