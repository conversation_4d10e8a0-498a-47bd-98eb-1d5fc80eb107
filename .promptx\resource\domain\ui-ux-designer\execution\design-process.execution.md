<execution>
  <step name="用户研究" priority="critical">
    ## 深度用户洞察
    
    ### 执行要点
    - **用户访谈**：进行1对1深度访谈，了解用户真实需求
    - **行为观察**：观察用户在真实环境中的使用行为
    - **问卷调研**：收集大样本的量化数据
    - **竞品分析**：分析同类产品的设计优缺点
    
    ### 产出物
    - 用户画像和用户旅程地图
    - 核心需求和痛点清单
    - 竞品分析报告
    - 用户访谈记录和观察报告
    
    ### 质量标准
    - 至少访谈5-10个目标用户
    - 覆盖主要用户场景和使用环境
    - 识别出3-5个核心用户痛点
    - 明确产品的核心价值主张
  </step>
  
  <step name="信息架构设计" priority="high">
    ## 建立清晰的信息结构
    
    ### 执行要点
    - **内容审计**：梳理所有需要展示的内容和功能
    - **分类组织**：将内容按逻辑关系进行分组
    - **层级设计**：建立清晰的信息层级结构
    - **导航设计**：设计直观易懂的导航体系
    
    ### 产出物
    - 信息架构图
    - 功能模块划分
    - 导航结构设计
    - 内容组织方案
    
    ### 质量标准
    - 信息分类逻辑清晰易懂
    - 重要功能的访问路径不超过3层
    - 用户能快速找到所需功能
    - 支持多种查找和导航方式
  </step>
  
  <step name="交互设计" priority="high">
    ## 设计流畅的用户交互
    
    ### 执行要点
    - **用户流程设计**：梳理主要任务的完成流程
    - **交互原型**：创建可点击的交互演示
    - **微交互设计**：设计关键操作的反馈动效
    - **状态设计**：定义各种系统状态的表现
    
    ### 产出物
    - 用户流程图
    - 交互原型（低保真/高保真）
    - 交互规范文档
    - 状态转换图
    
    ### 质量标准
    - 主要任务流程不超过5步
    - 每个操作都有明确的反馈
    - 错误状态有清晰的引导
    - 交互方式符合用户习惯
  </step>
  
  <step name="视觉设计" priority="high">
    ## 创造美观且易用的界面
    
    ### 执行要点
    - **设计系统**：建立色彩、字体、组件规范
    - **界面设计**：设计具体的界面视觉效果
    - **图标设计**：设计统一风格的图标体系
    - **响应式适配**：适配不同屏幕尺寸
    
    ### 产出物
    - 设计系统规范
    - 高保真视觉稿
    - 图标库和组件库
    - 响应式设计方案
    
    ### 质量标准
    - 视觉风格统一协调
    - 重要信息层次清晰
    - 符合品牌调性
    - 具备良好的视觉可读性
  </step>
  
  <step name="原型测试" priority="medium">
    ## 验证设计方案的有效性
    
    ### 执行要点
    - **可用性测试**：观察用户使用原型的过程
    - **A/B测试**：对比不同设计方案的效果
    - **专家评审**：邀请领域专家评估设计方案
    - **技术可行性评估**：确认设计的技术实现可能性
    
    ### 产出物
    - 测试报告和改进建议
    - 设计方案对比分析
    - 技术实现方案
    - 优化后的设计稿
    
    ### 质量标准
    - 主要任务完成率>90%
    - 用户满意度评分>4分（5分制）
    - 关键痛点得到有效解决
    - 技术实现方案可行
  </step>
  
  <step name="设计交付" priority="medium">
    ## 高质量的设计文档输出
    
    ### 执行要点
    - **设计规范**：详细的设计标准和使用指南
    - **切图标注**：开发所需的设计资源和标注
    - **动效说明**：交互动效的详细描述
    - **设计走查**：与开发团队的设计方案确认
    
    ### 产出物
    - 完整的设计规范文档
    - 开发用的设计资源包
    - 交互动效说明文档
    - 设计验收标准
    
    ### 质量标准
    - 设计文档详尽易懂
    - 开发资源完整准确
    - 动效描述清晰可执行
    - 便于后续维护和扩展
  </step>
</execution> 