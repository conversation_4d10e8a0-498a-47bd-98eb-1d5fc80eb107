{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-23T10:36:47.830Z", "updatedAt": "2025-06-23T10:36:47.832Z", "resourceCount": 14}, "resources": [{"id": "mail-automation-expert", "source": "project", "protocol": "role", "name": "Mail Automation Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/mail-automation-expert/mail-automation-expert.role.md", "metadata": {"createdAt": "2025-06-23T10:36:47.830Z", "updatedAt": "2025-06-23T10:36:47.830Z", "scannedAt": "2025-06-23T10:36:47.830Z"}}, {"id": "api-integration", "source": "project", "protocol": "thought", "name": "Api Integration 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/mail-automation-expert/thought/api-integration.thought.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "automation-thinking", "source": "project", "protocol": "thought", "name": "Automation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/mail-automation-expert/thought/automation-thinking.thought.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "api-best-practices", "source": "project", "protocol": "execution", "name": "Api Best Practices 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/mail-automation-expert/execution/api-best-practices.execution.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "mail-automation-workflow", "source": "project", "protocol": "execution", "name": "Mail Automation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/mail-automation-expert/execution/mail-automation-workflow.execution.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "automation-development", "source": "project", "protocol": "knowledge", "name": "Automation Development 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/mail-automation-expert/knowledge/automation-development.knowledge.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "mail-api-expertise", "source": "project", "protocol": "knowledge", "name": "Mail Api Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/mail-automation-expert/knowledge/mail-api-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "user-empathy", "source": "project", "protocol": "thought", "name": "User Empathy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/thought/user-empathy.thought.md", "metadata": {"createdAt": "2025-06-23T10:36:47.831Z", "updatedAt": "2025-06-23T10:36:47.831Z", "scannedAt": "2025-06-23T10:36:47.831Z"}}, {"id": "design-process", "source": "project", "protocol": "execution", "name": "Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/design-process.execution.md", "metadata": {"createdAt": "2025-06-23T10:36:47.832Z", "updatedAt": "2025-06-23T10:36:47.832Z", "scannedAt": "2025-06-23T10:36:47.832Z"}}, {"id": "user-experience-optimization", "source": "project", "protocol": "execution", "name": "User Experience Optimization 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-ux-designer/execution/user-experience-optimization.execution.md", "metadata": {"createdAt": "2025-06-23T10:36:47.832Z", "updatedAt": "2025-06-23T10:36:47.832Z", "scannedAt": "2025-06-23T10:36:47.832Z"}}, {"id": "design-tools-methods", "source": "project", "protocol": "knowledge", "name": "Design Tools Methods 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-ux-designer/knowledge/design-tools-methods.knowledge.md", "metadata": {"createdAt": "2025-06-23T10:36:47.832Z", "updatedAt": "2025-06-23T10:36:47.832Z", "scannedAt": "2025-06-23T10:36:47.832Z"}}, {"id": "ui-ux-expertise", "source": "project", "protocol": "knowledge", "name": "Ui Ux Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-ux-designer/knowledge/ui-ux-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-23T10:36:47.832Z", "updatedAt": "2025-06-23T10:36:47.832Z", "scannedAt": "2025-06-23T10:36:47.832Z"}}], "stats": {"totalResources": 14, "byProtocol": {"role": 2, "thought": 4, "execution": 4, "knowledge": 4}, "bySource": {"project": 14}}}