{"name": "xinlan-multi-mailbox-receiver", "version": "3.0.0", "description": "心蓝多邮箱接码 - Electron + React 版本", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "electron-builder", "preelectron-pack": "npm run build", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder", "dist-all": "npm run build && electron-builder -mwl"}, "dependencies": {"@headlessui/react": "^1.7.19", "@heroicons/react": "^2.2.0", "autoprefixer": "^10.4.16", "axios": "^1.6.0", "clsx": "^2.1.1", "electron-is-dev": "^2.0.0", "framer-motion": "^10.18.0", "lucide-react": "^0.292.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-query": "^3.39.3", "react-scripts": "^5.0.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.7"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4", "wait-on": "^7.2.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.xinlan.mailreceiver", "productName": "心蓝多邮箱接码", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "python-api/**/*"], "icon": "assets/icon-256x256.png", "mac": {"category": "public.app-category.productivity", "target": "dmg", "icon": "assets/icon-512x512.png"}, "win": {"target": "nsis", "requestedExecutionLevel": "asInvoker", "icon": "assets/icon-256x256.png"}, "linux": {"target": "AppImage", "category": "Office", "icon": "assets/app-icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/app-icon.ico", "uninstallerIcon": "assets/app-icon.ico", "installerHeaderIcon": "assets/app-icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true}}}