// API 服务，用于与 Electron 主进程通信

// 心蓝邮箱助手API基础URL
const API_URL = 'https://bsh.bhdata.com:30015/bhmailer';

/**
 * 保存配置到本地存储
 * @param {Object} config - 配置对象
 * @returns {Promise<Object>} - 保存结果
 */
export const saveConfig = async (config) => {
  try {
    if (!window.electronAPI) {
      console.warn('electronAPI 不可用，可能在浏览器环境中运行');
      return { success: false, error: 'electronAPI 不可用' };
    }
    return await window.electronAPI.saveConfig(config);
  } catch (error) {
    console.error('保存配置失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 从本地存储加载配置
 * @returns {Promise<Object>} - 加载的配置
 */
export const loadConfig = async () => {
  try {
    if (!window.electronAPI) {
      console.warn('electronAPI 不可用，可能在浏览器环境中运行');
      return { success: false, error: 'electronAPI 不可用' };
    }
    return await window.electronAPI.loadConfig();
  } catch (error) {
    console.error('加载配置失败:', error);
    return { success: false, error: error.message };
  }
};

/**
 * 检查邮件并提取验证码
 * @param {Object} params - 请求参数
 * @param {string} params.uid - 授权ID
 * @param {string} params.sign - API密钥
 * @param {string} params.email - 邮箱地址
 * @param {string} params.title - 邮件标题过滤
 * @param {string} params.fields - 提取字段规则
 * @param {number} params.minutesBack - 查询时间范围（分钟）
 * @returns {Promise<Object>} - API响应结果
 */
export const checkMail = async (params) => {
  try {
    const { uid, sign, email, title = '', from = '', fields, minutesBack = 3 } = params;
    
    // 计算查询时间范围（当前时间减去指定分钟数）
    const sent = Date.now() - (minutesBack * 60000);
    
    // 构建请求URL
    const url = `${API_URL}?uid=${uid}&sign=${sign}&act=checkMail&email=${encodeURIComponent(email)}&title=${encodeURIComponent(title)}&from=${encodeURIComponent(from)}&fields=${encodeURIComponent(fields)}&sent=${sent}&t=${Date.now()}`;
    
    // 发送请求
    const response = await fetch(url);
    const data = await response.json();
    
    // 如果返回code为5，表示邮件检查正在进行中，需要轮询获取结果
    if (data.code === 5) {
      return await pollResult(uid, sign, data.msg);
    }
    
    return data;
  } catch (error) {
    console.error('检查邮件失败:', error);
    return { code: -1, msg: `请求失败: ${error.message}` };
  }
};

/**
 * 轮询获取检查结果
 * @param {string} uid - 授权ID
 * @param {string} sign - API密钥
 * @param {string} id - 任务ID
 * @param {number} maxAttempts - 最大尝试次数
 * @returns {Promise<Object>} - API响应结果
 */
export const pollResult = async (uid, sign, id, maxAttempts = 12) => {
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      // 等待10秒
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // 构建请求URL
      const url = `${API_URL}?uid=${uid}&sign=${sign}&act=getResult&id=${id}&t=${Date.now()}`;
      
      // 发送请求
      const response = await fetch(url);
      const data = await response.json();
      
      // 如果不再是等待状态，返回结果
      if (data.code !== 5) {
        return data;
      }
      
      attempts++;
    } catch (error) {
      console.error('获取结果失败:', error);
      return { code: -1, msg: `请求失败: ${error.message}` };
    }
  }
  
  return { code: 7, msg: '智能等待超时' };
};

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {boolean} - 是否复制成功
 */
export const copyToClipboard = (text) => {
  try {
    if (window.electronAPI) {
      return window.electronAPI.copyToClipboard(text);
    } else {
      // 浏览器环境下使用 navigator.clipboard API
      navigator.clipboard.writeText(text);
      return true;
    }
  } catch (error) {
    console.error('复制到剪贴板失败:', error);
    return false;
  }
};

export default {
  saveConfig,
  loadConfig,
  checkMail,
  pollResult,
  copyToClipboard
}; 