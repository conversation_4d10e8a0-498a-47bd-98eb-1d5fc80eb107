<svg width="16" height="16" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="mailGradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shieldGradient16" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#mailGradient16)"/>
  
  <!-- 简化的邮件图标 -->
  <g transform="translate(128, 128)">
    <!-- 邮件本体 -->
    <rect x="-50" y="-30" width="100" height="70" rx="10" ry="10" fill="white" opacity="0.95"/>
    <!-- 邮件信封线条 -->
    <path d="M -50 -30 L 0 20 L 50 -30" stroke="#3B82F6" stroke-width="8" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- 验证码盾牌 -->
    <g transform="translate(35, -45)">
      <path d="M 0 -15 L 15 -10 L 15 10 C 15 15 10 20 0 25 C -10 20 -15 15 -15 10 L -15 -10 Z" fill="url(#shieldGradient16)"/>
      <!-- 盾牌对勾 -->
      <path d="M -8 0 L -3 5 L 8 -6" stroke="white" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
  </g>
</svg> 