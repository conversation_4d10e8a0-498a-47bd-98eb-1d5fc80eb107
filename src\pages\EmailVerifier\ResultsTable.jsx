import React from 'react';
import { Button } from '@/components/ui/button';
import { Copy, RotateCcw, Trash2 } from 'lucide-react';

const ResultsTable = ({ emails, emailResults, onCopy, onRetry, onDelete }) => {
  // 状态图标映射
  const getStatusIcon = (status) => {
    if (status === '成功' || status === '已复制') return '✅';
    if (status === '未找到') return '⚠️';
    if (status === '检查中') return '🔍';
    if (status === '失败') return '❌';
    return '⏳';
  };
  
  return (
    <div className="w-full h-full overflow-auto p-2">
      {emails.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <p className="text-sm text-muted-foreground">暂无邮箱数据，请先添加邮箱</p>
        </div>
      ) : (
        <div className="flex flex-col space-y-2">
          {emails.map((email, index) => {
            const result = emailResults[email] || { status: '等待检查', code: '' };
            const { status, code } = result;
            
            // 确定卡片边框颜色
            let borderColor = "border-gray-200";
            let bgColor = "bg-white";
            if (status === '成功' || status === '已复制') {
              borderColor = "border-green-300";
              bgColor = "bg-green-50";
            }
            else if (status === '未找到') {
              borderColor = "border-yellow-300";
              bgColor = "bg-yellow-50";
            }
            else if (status === '失败') {
              borderColor = "border-red-300";
              bgColor = "bg-red-50";
            }
            else if (status === '检查中') {
              borderColor = "border-blue-300";
              bgColor = "bg-blue-50";
            }
            
            return (
              <div 
                key={email} 
                className={`${bgColor} rounded-md border ${borderColor} shadow-sm p-3 flex items-center h-16`}
              >
                {/* 序号和邮箱 */}
                <div className="flex-shrink-0 w-[200px] mr-4">
                  <div className="flex items-center">
                    <span className="text-sm font-medium mr-2">#{index + 1}</span>
                    <span className="text-sm font-medium truncate" title={email}>
                      {email}
                    </span>
                  </div>
                </div>
                
                {/* 状态图标 */}
                <div className="flex-shrink-0 w-10 mr-4 text-center">
                  <span title={status} className="text-lg">{getStatusIcon(status)}</span>
                </div>
                
                {/* 验证码 */}
                <div className="flex-grow bg-white rounded px-3 py-2 border border-gray-100 mr-4">
                  {code && code !== '未找到邮件' ? (
                    <span className="text-base font-mono font-bold">{code}</span>
                  ) : (
                    <span className="text-sm text-muted-foreground italic">无验证码</span>
                  )}
                </div>
                
                {/* 操作按钮 */}
                <div className="flex-shrink-0 flex space-x-2">
                  {code && code !== '未找到邮件' && (
                    <button
                      onClick={() => onCopy(email)}
                      className="h-8 w-8 rounded-full flex items-center justify-center bg-green-100 hover:bg-green-200 transition-colors"
                      title="复制验证码"
                    >
                      <Copy className="h-4 w-4 text-green-700" />
                    </button>
                  )}
                  <button
                    onClick={() => onRetry(email)}
                    className="h-8 w-8 rounded-full flex items-center justify-center bg-blue-100 hover:bg-blue-200 transition-colors"
                    title="重新获取"
                  >
                    <RotateCcw className="h-4 w-4 text-blue-700" />
                  </button>
                  <button
                    onClick={() => onDelete(email)}
                    className="h-8 w-8 rounded-full flex items-center justify-center bg-red-100 hover:bg-red-200 transition-colors"
                    title="删除邮箱"
                  >
                    <Trash2 className="h-4 w-4 text-red-700" />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default ResultsTable; 