# 🚀 现代化邮箱验证码工具 v3.0

> **Electron + React + Python** 技术栈打造的超现代化邮箱验证码获取工具  
> 媲美 **Slack、Notion** 的精美界面设计 + 强大的后端处理能力

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)
![Version](https://img.shields.io/badge/version-3.0.0-green.svg)

## ✨ 项目亮点

### 🎨 **超现代化 UI 设计**
- 🌟 **Apple/Notion 风格设计语言** - 简约、优雅、直观
- 🎭 **Framer Motion 动画效果** - 流畅的过渡和交互动画
- 🎯 **TailwindCSS 响应式布局** - 完美适配各种屏幕尺寸
- 🖼️ **Lucide React 图标库** - 精美的矢量图标系统
- 🌈 **智能配色系统** - 支持主题色彩和状态指示

### ⚡ **技术架构优势**
- 🖥️ **Electron 桌面应用** - 跨平台原生体验
- ⚛️ **React 18 + Hooks** - 现代化前端框架
- 🐍 **FastAPI 异步后端** - 高性能 Python API 服务
- 🔄 **实时状态同步** - WebSocket + 轮询双重保障
- 📦 **模块化组件设计** - 可维护、可扩展的代码架构

### 🚀 **功能特性**
- 📧 **多邮箱批量处理** - 支持无限数量邮箱同时验证
- ⚡ **双模式验证** - 立即检查 + 智能等待两种模式
- 🎯 **智能验证码提取** - 支持多种正则表达式规则
- 📋 **一键复制功能** - 快速复制验证码到剪贴板
- 💾 **配置自动保存** - 用户设置持久化存储
- 🔍 **实时日志监控** - 详细的操作日志和错误追踪

## 🏗️ 项目架构

```
modern-mail-verification-tool/
├── 📁 src/                     # React 前端源码
│   ├── App.js                  # 主应用组件
│   ├── App.css                 # 样式文件
│   └── components/             # 组件目录
├── 📁 public/                  # 静态资源
│   ├── electron.js             # Electron 主进程
│   ├── preload.js              # 预加载脚本
│   └── index.html              # HTML 模板
├── 📁 python-api/              # Python 后端 API
│   ├── main.py                 # FastAPI 服务器
│   └── requirements.txt        # Python 依赖
├── 📁 assets/                  # 应用资源
│   └── icon.png                # 应用图标
├── package.json                # Node.js 配置
├── tailwind.config.js          # TailwindCSS 配置
└── start-dev.bat               # 开发启动脚本
```

## 🛠️ 环境要求

### 必需环境
- **Node.js** >= 16.0.0
- **Python** >= 3.8.0
- **npm** 或 **yarn**

### 推荐环境
- **Node.js** 18+ (LTS 版本)
- **Python** 3.10+
- **Windows 10/11** 或 **macOS 10.15+** 或 **Ubuntu 20.04+**

## 🚀 快速开始

### 1️⃣ 克隆项目
```bash
git clone https://github.com/your-username/modern-mail-verification-tool.git
cd modern-mail-verification-tool
```

### 2️⃣ 一键启动开发环境 (Windows)
```bash
# 双击运行或在命令行执行
start-dev.bat
```

### 3️⃣ 手动启动 (跨平台)

#### 安装前端依赖
```bash
npm install
```

#### 安装后端依赖
```bash
cd python-api
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate

pip install fastapi uvicorn requests pydantic[email] python-multipart
```

#### 启动后端服务
```bash
cd python-api
python main.py
```

#### 启动前端应用
```bash
# 新终端窗口
npm run electron-dev
```

## 💡 使用指南

### 🔑 API 配置
1. 在左侧面板输入**授权 ID** 和 **API 密钥**
2. 系统会自动保存配置信息

### 📧 邮箱管理
1. 在**邮箱地址**文本框中输入邮箱（每行一个）
2. 点击**添加邮箱**按钮
3. 可以随时删除不需要的邮箱

### ⚙️ 参数设置
- **邮件标题包含**: 过滤包含特定关键词的邮件
- **查询模式**: 
  - **立即查询** - 立即检查一次
  - **智能等待** - 持续监控直到找到验证码

### 🚀 开始验证
1. 确保已配置 API 信息和添加邮箱
2. 点击**立即获取**或**智能等待**按钮
3. 在右侧查看实时状态和结果
4. 点击复制按钮快速复制验证码

## 🎨 界面预览

### 🌟 主界面设计
- **左侧配置面板** - 简洁的设置区域
- **右侧结果展示** - 卡片式邮箱状态展示
- **实时状态同步** - 动态更新验证进度
- **优雅的动画效果** - 流畅的交互体验

### 🎯 设计亮点
- **渐变背景** - 现代化的视觉层次
- **卡片阴影** - 立体感的界面元素
- **状态徽章** - 直观的处理状态指示
- **响应式布局** - 适配不同窗口尺寸

## 🔧 开发指南

### 🏃‍♂️ 开发模式
```bash
# 前端热重载开发
npm start

# Electron 开发模式
npm run electron-dev

# 后端开发模式
cd python-api
python main.py
```

### 📦 构建打包
```bash
# 构建 React 应用
npm run build

# 打包 Electron 应用
npm run dist

# 构建所有平台
npm run dist-all
```

### 🧪 测试
```bash
# 运行前端测试
npm test

# 后端 API 测试
cd python-api
pytest
```

## 📚 API 文档

### 🔌 后端 API 端点
- **POST** `/api/config` - 设置 API 配置
- **POST** `/api/emails/verify` - 批量验证邮箱
- **GET** `/api/tasks/{task_id}` - 获取任务状态
- **GET** `/api/health` - 健康检查

### 📖 详细文档
启动后端服务后访问: `http://localhost:8000/docs`

## 🤝 贡献指南

### 🛠️ 开发流程
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 📝 代码规范
- **React**: 使用 Hooks 和函数组件
- **Python**: 遵循 PEP 8 规范
- **TypeScript**: 推荐使用类型注解
- **Commit**: 使用语义化提交信息

## 📄 许可证

本项目基于 [MIT 许可证](LICENSE) 开源。

## 🙏 致谢

- **React** - 现代化前端框架
- **Electron** - 跨平台桌面应用开发
- **TailwindCSS** - 实用优先的CSS框架
- **FastAPI** - 现代化Python Web框架
- **Framer Motion** - 强大的动画库
- **Lucide React** - 美观的图标库

## 📞 支持与反馈

- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-username/modern-mail-verification-tool/issues)
- 💡 **功能建议**: [GitHub Discussions](https://github.com/your-username/modern-mail-verification-tool/discussions)
- 📧 **邮件联系**: <EMAIL>

---

<div align="center">

**🌟 如果这个项目对您有帮助，请给个 Star ⭐**

Made with ❤️ by [Your Name]

</div> 