import React from "react";
import { Input } from "@/components/ui/input";
import { HelpCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const SettingsPanel = ({
  titleFilter,
  setTitleFilter,
  timeRange,
  setTimeRange,
  extractRule,
  setExtractRule,
  checkMode,
  setCheckMode,
  waitTime,
  setWaitTime,
}) => {
  const extractRules = [
    { value: "$BODYTEXT-R|\\d{6}$", label: "6位数字" },
    { value: "$BODYTEXT-R|\\d{4}$", label: "4位数字" },
    { value: "$BODYTEXT-R|\\d{4,8}$", label: "4-8位数字" },
    { value: "$BODYTEXT-R|[A-Za-z0-9]{6,}$", label: "6位字母数字" },
    { value: "$BODYTEXT-R|\\d{6,}$", label: "6位以上数字" },
    { value: "$BODYTEXT$", label: "完整邮件内容" }
  ];
  
  const { toast } = useToast();
  
  const showExtractRulesHelp = () => {
    toast({
      title: "验证码提取规则说明",
      description: "常用规则：6位数字=123456，4位数字=1234，完整邮件内容=手动查找",
      duration: 5000,
    });
  };
  
  return (
    <div className="space-y-1.5 text-xs">
      {/* 标题过滤 */}
      <div className="space-y-1">
        <label className="text-xs text-gray-500">邮件标题包含</label>
        <Input
          value={titleFilter}
          onChange={(e) => setTitleFilter(e.target.value)}
          className="h-6 text-xs"
        />
      </div>
      
      {/* 时间范围 */}
      <div className="space-y-1">
        <label className="text-xs text-gray-500">查询时间范围</label>
        <div className="flex items-center">
          <Input
            type="number"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="h-6 text-xs w-16"
          />
          <span className="ml-2 text-xs text-gray-500">分钟内的邮件</span>
        </div>
      </div>
      
      {/* 查询模式 */}
      <div className="space-y-1">
        <label className="text-xs text-gray-500">查询模式</label>
        <div className="flex space-x-4">
          <div 
            className={`flex items-center cursor-pointer ${checkMode === 'immediate' ? 'text-blue-600' : 'text-gray-600'}`}
            onClick={() => setCheckMode('immediate')}
          >
            <div className={`w-3 h-3 rounded-full mr-1 border ${checkMode === 'immediate' ? 'bg-blue-600 border-blue-600' : 'bg-white border-gray-400'}`}></div>
            <span>⚡立即查询</span>
          </div>
          <div 
            className={`flex items-center cursor-pointer ${checkMode === 'smart_wait' ? 'text-blue-600' : 'text-gray-600'}`}
            onClick={() => setCheckMode('smart_wait')}
          >
            <div className={`w-3 h-3 rounded-full mr-1 border ${checkMode === 'smart_wait' ? 'bg-blue-600 border-blue-600' : 'bg-white border-gray-400'}`}></div>
            <span>🔄智能等待</span>
          </div>
        </div>
      </div>
      
      {/* 等待时间 */}
      {checkMode === 'smart_wait' && (
        <div className="space-y-1">
          <label className="text-xs text-gray-500">最大等待时间</label>
          <div className="flex items-center">
            <Input
              type="number"
              value={waitTime}
              onChange={(e) => setWaitTime(e.target.value)}
              className="h-6 text-xs w-16"
            />
            <span className="ml-2 text-xs text-gray-500">秒 (每10秒检查一次)</span>
          </div>
        </div>
      )}
      
      {/* 提取规则 */}
      <div className="space-y-1">
        <div className="flex items-center">
          <label className="text-xs text-gray-500">验证码提取规则</label>
          <HelpCircle 
            className="h-3 w-3 ml-1 cursor-pointer text-gray-400" 
            onClick={showExtractRulesHelp}
          />
        </div>
        <select
          value={extractRule}
          onChange={(e) => setExtractRule(e.target.value)}
          className="w-full h-6 text-xs border rounded px-2 bg-white"
        >
          {extractRules.map((rule) => (
            <option key={rule.value} value={rule.value}>
              {rule.label}
            </option>
          ))}
        </select>
      </div>
      
      <div className="text-xs text-gray-400 mt-1">
        💡 常用规则：6位数字=123456，4位数字=1234
      </div>
    </div>
  );
};

export default SettingsPanel;



