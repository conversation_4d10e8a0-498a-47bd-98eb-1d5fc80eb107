# 心蓝邮箱助手API专业知识体系

## 核心API接口规范

### 基础认证机制
- **授权ID（uid）**：在心蓝邮箱助手【文件】-【插件管理】-【HTTP API插件】中获取
- **API密钥（sign）**：同上位置获取，用于请求签名验证
- **时间戳（t）**：13位毫秒时间戳，防止重放攻击
- **API地址**：`https://bsh.bhdata.com:30015/bhmailer`

### API接口分类

#### 1. 邮件检查接口（checkMail）
**功能**：检查指定邮箱是否有符合条件的新邮件并提取内容

**请求参数**：
- `uid`: 授权ID
- `sign`: API密钥  
- `act`: 操作类型，固定为"checkMail"
- `email`: 目标邮箱地址
- `title`: 邮件主题匹配条件（可选）
- `from`: 发件人匹配条件（可选）  
- `sent`: 邮件发送时间范围，负数表示当前时间前N毫秒
- `fields`: 内容提取规则（重要）
- `t`: 当前时间戳

**响应格式**：
```json
{
  "code": 0,     // 0:成功, 5:处理中, -1:错误
  "msg": "结果内容或任务ID或错误信息"
}
```

#### 2. 结果获取接口（getResult）
**功能**：获取异步任务的处理结果

**请求参数**：
- `uid`: 授权ID
- `sign`: API密钥
- `act`: 操作类型，固定为"getResult"  
- `id`: 任务ID（从checkMail返回的code=5时的msg字段获取）
- `t`: 当前时间戳

**使用场景**：当checkMail返回code=5时，表示任务正在后台处理，需要轮询此接口获取结果

### 内容提取规则（fields参数）

#### 基础语法
- `$TITLE$`：邮件主题
- `$FROM$`：发件人地址
- `$BODYTEXT$`：邮件可见文本内容
- `$BODYCLEAR$`：邮件纯文本内容（去除HTML）
- `$BODY$`：邮件HTML源代码
- `$RECEIVED$`：邮件接收时间

#### 正则表达式提取
- `$BODYTEXT-R|正则表达式$`：从邮件文本中用正则提取内容
- `$TITLE-R|正则表达式$`：从邮件主题中用正则提取内容

#### 常用验证码提取规则
```javascript
// 6位数字验证码
$BODYTEXT-R|\d{6}$

// 4-8位数字验证码  
$BODYTEXT-R|\d{4,8}$

// 冒号后的6位数字
$BODYTEXT-R|(?<=[：:]\s*)\d{6}(?!\d)$

// 大小写字母+数字组合（10位）
$BODYTEXT-R|[0-9a-zA-Z]{10}$

// 提取第一个链接
$BODY|<a href="|"$
```

#### 复合内容模板
```html
$TITLE$<br><br>
<span style="font-weight:bold">发件人：</span>$HTMLENCODE-FROM$<br><br>
<span style="font-weight:bold">发送时间：</span>$RECEIVED$<br><br>
您的验证码是：<span style="color:red;font-size:18px">$BODYTEXT-R|\d{6}$</span>
```

## API调用频率限制

### 调用统计规则
- 只统计`checkMail`、`getMail`、`sentMail`的调用次数
- `getResult`不计入调用次数统计

### 频率控制建议
- 建议间隔**10秒以上**再进行下次checkMail调用
- 异步任务轮询间隔建议**10-15秒**
- 避免短时间内大量并发请求

## 错误处理策略

### 常见错误码及处理
- **code: 0** - 成功，直接使用msg字段的内容
- **code: 5** - 任务处理中，需要用msg作为任务ID轮询getResult
- **code: -1** - 错误，msg字段包含错误信息

### HTTP状态码处理
- **200** - 请求成功，检查业务code
- **403** - 认证失败，检查uid和sign是否正确
- **429** - 请求过于频繁，需要减慢调用频率
- **500** - 服务器错误，可以重试

### 网络异常处理
- **连接超时**：默认30秒超时，可重试3次
- **读取超时**：心蓝服务响应较慢时常见，延长超时时间
- **网络中断**：实现指数退避重试策略

## 邮箱类型支持

### 主流邮箱服务商
- **微软邮箱**：outlook.com, hotmail.com, live.com
- **谷歌邮箱**：gmail.com  
- **QQ邮箱**：qq.com
- **网易邮箱**：163.com, 126.com
- **阿里邮箱**：aliyun.com
- **企业邮箱**：自定义域名邮箱

### 邮箱配置要求
- 需要在心蓝邮箱助手中预先添加并配置好邮箱
- 确保邮箱能正常收信
- API调用时可传入密码自动添加邮箱

## 性能优化要点

### 提取规则优化
- 使用精确的正则表达式，避免过度匹配
- 优先从BODYTEXT提取，避免使用BODY（HTML源码）
- 合理使用正则断言，提高匹配准确性

### 轮询策略优化
- 异步任务轮询间隔逐步增长：10s → 15s → 20s
- 设置最大轮询时间（建议5分钟）
- 轮询失败时实现重试机制

### 缓存策略  
- 对于相同条件的查询，短时间内避免重复调用
- 缓存邮箱配置信息，减少配置读取次数
- 合理缓存提取规则，避免重复编译正则表达式

## 安全最佳实践

### 敏感信息保护
- API密钥必须加密存储，不得明文保存
- 邮箱密码使用强加密算法保护
- 日志中不记录完整的API密钥和密码

### 请求安全
- 使用HTTPS协议确保传输安全
- 实现请求签名验证（如果API支持）
- 设置合理的超时时间防止长时间等待

### 数据安全  
- 验证码等敏感信息及时清理
- 实现数据脱敏功能
- 提供数据删除和清理功能

## 故障排查指南

### 常见问题及解决方案

#### 1. 认证失败
**症状**：返回HTTP 403或业务错误
**排查**：
- 检查uid和sign是否正确
- 确认心蓝邮箱助手HTTP API插件已启用
- 验证时间戳格式是否正确

#### 2. 无法获取邮件
**症状**：返回成功但msg为空或无内容
**排查**：
- 确认邮箱中确实有符合条件的邮件
- 检查title和from匹配条件是否过于严格
- 验证sent时间范围设置是否合理
- 确认邮箱在心蓝助手中配置正确

#### 3. 验证码提取失败
**症状**：返回成功但提取不到验证码
**排查**：
- 检查正则表达式是否正确
- 尝试使用$BODYTEXT$查看完整邮件内容
- 确认验证码格式与正则规则匹配
- 测试不同的提取字段（BODYTEXT vs BODYCLEAR）

#### 4. 请求超时
**症状**：请求长时间无响应
**排查**：
- 检查网络连接稳定性
- 适当增加超时时间设置
- 确认心蓝助手服务运行正常
- 尝试减少并发请求数量

### 调试技巧
- 使用简单的提取规则（如$BODYTEXT$）查看原始内容
- 逐步细化匹配条件，从宽松到精确
- 在心蓝助手中测试邮件规则功能
- 查看心蓝助手的日志输出

## 高级功能应用

### 批量邮箱监控
```python
# 示例：同时监控多个邮箱
emails = ['<EMAIL>', '<EMAIL>'] 
for email in emails:
    result = api_client.check_mail(
        email=email,
        title="验证码",
        fields="$BODYTEXT-R|\d{6}$"
    )
```

### 智能提取规则
```python
# 示例：多种格式的验证码提取
patterns = [
    r'\d{6}',           # 纯数字
    r'[A-Z]{4}\d{2}',   # 字母+数字
    r'\d{4}-\d{2}',     # 带分隔符
]

for pattern in patterns:
    fields = f"$BODYTEXT-R|{pattern}$"
    # 尝试提取...
```

### 条件化处理
```python
# 示例：根据发件人使用不同提取规则
sender_rules = {
    '<EMAIL>': r'\d{6}',
    '<EMAIL>': r'[A-Z0-9]{8}',
    'default': r'\d{4,8}'
}

rule = sender_rules.get(from_addr, sender_rules['default'])
fields = f"$BODYTEXT-R|{rule}$"
``` 