import React, { useState, useEffect } from 'react';
import { Mail, Settings, RotateCcw, Clipboard, Trash2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { saveConfig, loadConfig, checkMail, copyToClipboard } from '@/utils/api';
import ResultsTable from './ResultsTable';
import LogViewer from './LogViewer';
import SettingsPanel from './SettingsPanel';

const EmailVerifier = () => {
  // 状态管理
  const [uid, setUid] = useState('');
  const [sign, setSign] = useState('');
  const [emailsText, setEmailsText] = useState('');
  const [emails, setEmails] = useState([]);
  const [emailResults, setEmailResults] = useState({});
  const [processing, setProcessing] = useState(false);
  const [titleFilter, setTitleFilter] = useState('验证码');
  const [timeRange, setTimeRange] = useState('3');
  const [extractRule, setExtractRule] = useState('$BODYTEXT-R|\\d{6}$');
  const [checkMode, setCheckMode] = useState('immediate');
  const [waitTime, setWaitTime] = useState('120');
  const [logs, setLogs] = useState([]);

  const { toast } = useToast();

  // 加载配置
  useEffect(() => {
    const config = loadConfig();
    if (config) {
      setUid(config.uid || '');
      setSign(config.sign || '');
      setEmails(config.emails || []);
      setTitleFilter(config.titleFilter || '验证码');
      setTimeRange(config.timeRange || '3');
      setExtractRule(config.extractRule || '$BODYTEXT-R|\\d{6}$');
      setCheckMode(config.checkMode || 'immediate');
      setWaitTime(config.waitTime || '120');
      addLog('配置已加载');
    }
  }, []);

  // 保存配置
  const saveCurrentConfig = () => {
    saveConfig({
      uid,
      sign,
      emails,
      titleFilter,
      timeRange,
      extractRule,
      checkMode,
      waitTime
    });
    addLog('配置已保存');
    toast({
      title: '配置已保存',
      description: `已保存 ${emails.length} 个邮箱和相关设置`
    });
  };

  // 添加日志
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  // 添加邮箱
  const addEmails = () => {
    if (!emailsText.trim()) return;
    
    const newEmails = emailsText.trim().split('\n').filter(email => email.trim());
    const uniqueEmails = [...new Set([...emails, ...newEmails])];
    
    setEmails(uniqueEmails);
    setEmailsText('');
    addLog(`添加了 ${newEmails.length} 个邮箱`);
    
    toast({
      title: '邮箱已添加',
      description: `成功添加 ${newEmails.length} 个邮箱`
    });
  };

  // 清空邮箱
  const clearEmails = () => {
    setEmails([]);
    setEmailResults({});
    addLog('已清空邮箱列表');
    
    toast({
      title: '邮箱已清空',
      description: '已删除所有邮箱和结果'
    });
  };

  // 检查单个邮箱
  const checkSingleEmail = async (email) => {
    try {
      addLog(`开始检查邮箱: ${email}`);
      
      // 更新状态为检查中
      setEmailResults(prev => ({
        ...prev,
        [email]: { status: '检查中', code: '' }
      }));
      
      // 调用API检查邮箱
      const result = await checkMail({
        uid,
        sign,
        email,
        title: titleFilter,
        fields: extractRule,
        minutes: parseInt(timeRange)
      });
      
      if (result.success) {
        setEmailResults(prev => ({
          ...prev,
          [email]: { 
            status: '成功', 
            code: result.code || '',
            message: result.message || ''
          }
        }));
        addLog(`${email}: 成功获取验证码`);
      } else {
        setEmailResults(prev => ({
          ...prev,
          [email]: { 
            status: result.code === 7 ? '未找到' : '失败', 
            code: '',
            message: result.message || '检查失败'
          }
        }));
        addLog(`${email}: ${result.message || '检查失败'}`);
      }
      
      return result;
    } catch (error) {
      console.error('检查邮箱出错:', error);
      setEmailResults(prev => ({
        ...prev,
        [email]: { status: '失败', code: '', message: error.message }
      }));
      addLog(`${email}: 检查出错 - ${error.message}`);
      return { success: false, message: error.message };
    }
  };

  // 智能等待检查
  const checkWithSmartWait = async (email) => {
    const maxWaitTime = parseInt(waitTime);
    const startTime = Date.now();
    let attempts = 0;
    
    addLog(`开始智能等待检查: ${email}，最大等待时间: ${maxWaitTime}秒`);
    
    while (Date.now() - startTime < maxWaitTime * 1000) {
      attempts++;
      
      const result = await checkSingleEmail(email);
      
      if (result.success && result.code) {
        addLog(`${email}: 在第 ${attempts} 次尝试中找到验证码`);
        return result;
      }
      
      if (Date.now() - startTime >= maxWaitTime * 1000 - 10000) {
        break;
      }
      
      addLog(`${email}: 等待10秒后进行第 ${attempts + 1} 次尝试`);
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
    
    addLog(`${email}: 智能等待超时，共尝试 ${attempts} 次`);
    return { 
      success: false, 
      code: 7, 
      message: `智能等待超时 (尝试了${attempts}次)`
    };
  };

  // 检查所有邮箱
  const checkAllEmails = async () => {
    if (processing) return;
    
    if (!uid || !sign) {
      toast({
        title: '错误',
        description: '请先配置API信息',
        variant: 'destructive'
      });
      return;
    }
    
    if (emails.length === 0) {
      toast({
        title: '错误',
        description: '请先添加邮箱地址',
        variant: 'destructive'
      });
      return;
    }
    
    setProcessing(true);
    
    if (checkMode === 'immediate') {
      addLog(`开始立即检查 ${emails.length} 个邮箱`);
    } else {
      addLog(`开始智能等待检查 ${emails.length} 个邮箱，最大等待时间: ${waitTime}秒`);
    }
    
    // 重置所有邮箱状态
    const initialResults = {};
    emails.forEach(email => {
      initialResults[email] = { status: '检查中', code: '' };
    });
    setEmailResults(initialResults);
    
    try {
      // 并发检查所有邮箱，最多5个并发
      const results = [];
      const batchSize = 5;
      
      for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);
        const batchPromises = batch.map(email => 
          checkMode === 'immediate' 
            ? checkSingleEmail(email)
            : checkWithSmartWait(email)
        );
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }
      
      // 计算结果统计
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;
      
      addLog(`批量检查完成: 成功 ${successCount} 个, 失败 ${failCount} 个`);
      
      toast({
        title: '检查完成',
        description: `成功: ${successCount}, 失败: ${failCount}`
      });
    } catch (error) {
      console.error('批量检查出错:', error);
      addLog(`批量检查出错: ${error.message}`);
      
      toast({
        title: '检查出错',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setProcessing(false);
    }
  };

  // 复制验证码
  const copyCode = (email) => {
    const result = emailResults[email];
    if (result && result.code) {
      copyToClipboard(result.code);
      
      // 更新状态
      setEmailResults(prev => ({
        ...prev,
        [email]: { ...prev[email], status: '已复制' }
      }));
      
      addLog(`已复制验证码: ${result.code} (邮箱: ${email})`);
      
      toast({
        title: '验证码已复制',
        description: `已复制: ${result.code}`
      });
    } else {
      addLog(`邮箱 ${email} 暂无验证码可复制`);
      
      toast({
        title: '无法复制',
        description: '该邮箱暂无验证码',
        variant: 'destructive'
      });
    }
  };

  // 重试单个邮箱
  const retrySingleEmail = async (email) => {
    if (processing) {
      toast({
        title: '正在处理中',
        description: '请等待当前操作完成',
        variant: 'destructive'
      });
      return;
    }
    
    addLog(`重新检查邮箱: ${email}`);
    
    // 重置状态
    setEmailResults(prev => ({
      ...prev,
      [email]: { status: '检查中', code: '' }
    }));
    
    try {
      if (checkMode === 'immediate') {
        await checkSingleEmail(email);
      } else {
        await checkWithSmartWait(email);
      }
    } catch (error) {
      console.error('重试出错:', error);
      addLog(`重试出错: ${error.message}`);
      
      toast({
        title: '重试出错',
        description: error.message,
        variant: 'destructive'
      });
    }
  };

  // 删除单个邮箱
  const deleteEmail = (email) => {
    setEmails(prev => prev.filter(e => e !== email));
    setEmailResults(prev => {
      const newResults = { ...prev };
      delete newResults[email];
      return newResults;
    });
    
    addLog(`已删除邮箱: ${email}`);
    
    toast({
      title: '邮箱已删除',
      description: `已删除: ${email}`
    });
  };

  // 复制所有验证码
  const copyAllCodes = () => {
    const codesWithEmails = Object.entries(emailResults)
      .filter(([_, result]) => result.code)
      .map(([email, result]) => `${email}: ${result.code}`)
      .join('\n');
    
    if (codesWithEmails) {
      copyToClipboard(codesWithEmails);
      
      const count = Object.values(emailResults).filter(r => r.code).length;
      addLog(`已复制 ${count} 个验证码到剪贴板`);
      
      toast({
        title: '全部验证码已复制',
        description: `已复制 ${count} 个验证码`
      });
    } else {
      addLog('暂无验证码可复制');
      
      toast({
        title: '无法复制',
        description: '暂无验证码可复制',
        variant: 'destructive'
      });
    }
  };

  // 清空结果
  const clearResults = () => {
    const newResults = {};
    emails.forEach(email => {
      newResults[email] = { status: '等待检查', code: '' };
    });
    
    setEmailResults(newResults);
    addLog('已清空所有验证码结果');
    
    toast({
      title: '结果已清空',
      description: '已清空所有验证码结果'
    });
  };

  return (
    <div className="flex flex-col h-screen">
      {/* 顶部标题栏 */}
      <header className="bg-white border-b p-3 shadow-sm">
        <div className="flex justify-between items-center">
          <h1 className="text-lg font-bold flex items-center gap-2">
            <Mail className="h-5 w-5" /> 邮箱验证码查询工具
          </h1>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span>PC桌面版 v2.3</span>
            <span>|</span>
            <span>{new Date().toLocaleString()}</span>
          </div>
        </div>
      </header>

      {/* 主内容区 - 两行布局 */}
      <div className="flex-1 p-2 flex flex-col space-y-2">
        {/* 第一行 - 配置和结果 */}
        <div className="grid grid-cols-12 gap-2 flex-1 min-h-0">
          {/* 左侧配置区 - 竖向排列所有卡片，整体靠上对齐，无需滚动 */}
          <div className="col-span-5 flex flex-col items-start justify-start h-full">
            {/* 内容容器 - 固定宽度，靠上对齐 */}
            <div className="w-full flex flex-col gap-2 px-4 h-full">
              {/* API 配置卡片 - 减小高度 */}
              <Card className="shadow">
                <CardHeader className="py-1 px-3">
                  <CardTitle className="text-base flex items-center gap-1">
                    🔐 API 配置
                  </CardTitle>
                </CardHeader>
                <CardContent className="py-2 px-3">
                  {/* 授权ID和API密钥放在同一行 */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <label className="text-xs text-gray-500">授权ID</label>
                      <Input 
                        value={uid} 
                        onChange={(e) => setUid(e.target.value)}
                        className="h-7 text-sm"
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="text-xs text-gray-500">API密钥</label>
                      <Input 
                        type="password"
                        value={sign} 
                        onChange={(e) => setSign(e.target.value)}
                        className="h-7 text-sm"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 邮箱管理卡片 - 减小高度 */}
              <Card className="shadow">
                <CardHeader className="py-1 px-3">
                  <CardTitle className="text-base flex items-center gap-1">
                    📧 邮箱管理
                  </CardTitle>
                </CardHeader>
                <CardContent className="py-2 px-3">
                  <div className="space-y-1">
                    <label className="text-xs text-gray-500">邮箱列表</label>
                    <Textarea 
                      value={emailsText}
                      onChange={(e) => setEmailsText(e.target.value)}
                      placeholder="每行一个邮箱地址"
                      className="resize-none h-14 text-sm"
                    />
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Button 
                      size="sm" 
                      onClick={addEmails}
                      className="h-6 text-xs"
                    >
                      ➕ 添加
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive" 
                      onClick={clearEmails}
                      className="h-6 text-xs"
                    >
                      🗑️ 清空
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 参数设置卡片 - 减小高度 */}
              <Card className="shadow">
                <CardHeader className="py-1 px-3">
                  <CardTitle className="text-base flex items-center gap-1">
                    ⚙️ 参数设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="py-2 px-3">
                  <SettingsPanel 
                    titleFilter={titleFilter}
                    setTitleFilter={setTitleFilter}
                    timeRange={timeRange}
                    setTimeRange={setTimeRange}
                    extractRule={extractRule}
                    setExtractRule={setExtractRule}
                    checkMode={checkMode}
                    setCheckMode={setCheckMode}
                    waitTime={waitTime}
                    setWaitTime={setWaitTime}
                  />
                </CardContent>
              </Card>

              {/* 主操作按钮 - 固定在底部 */}
              <div className="mt-auto">
                <Button 
                  className="w-full py-2 text-base font-bold"
                  disabled={processing}
                  onClick={checkAllEmails}
                >
                  {processing ? '🔄 处理中...' : checkMode === 'immediate' ? '⚡ 立即检查' : '🔄 智能等待'}
                </Button>
              </div>
            </div>
          </div>

          {/* 右侧结果区 - 内容居中 */}
          <div className="col-span-7 flex items-center justify-center">
            <div className="w-[98%] h-full flex flex-col space-y-2">
              {/* 验证结果卡片 - 表格可滚动 */}
              <Card className="shadow flex-1 flex flex-col min-h-0">
                <CardHeader className="py-1 px-3 shrink-0">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-base flex items-center gap-1">
                      📋 邮箱状态
                    </CardTitle>
                    <span className="text-xs text-gray-500">
                      共 {emails.length} 个邮箱
                    </span>
                  </div>
                </CardHeader>
                <CardContent className="p-0 flex-1 overflow-auto">
                  <ResultsTable 
                    emails={emails}
                    results={emailResults}
                    onCopy={copyCode}
                    onRetry={retrySingleEmail}
                    onDelete={deleteEmail}
                  />
                </CardContent>
                <CardFooter className="py-1 px-3 border-t shrink-0">
                  <div className="flex gap-2 w-full">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1 h-6 text-xs"
                      onClick={() => emails.filter(email => 
                        !emailResults[email]?.code
                      ).forEach(retrySingleEmail)}
                    >
                      🔄 重试失败
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1 h-6 text-xs"
                      onClick={copyAllCodes}
                    >
                      📋 复制全部
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1 h-6 text-xs"
                      onClick={clearResults}
                    >
                      🗑️ 清空结果
                    </Button>
                  </div>
                </CardFooter>
              </Card>

              {/* 日志区域 - 固定高度 */}
              <Card className="shadow h-[120px] shrink-0">
                <CardHeader className="py-1 px-3">
                  <CardTitle className="text-base flex items-center gap-1">
                    📝 日志
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 h-[85px]">
                  <LogViewer logs={logs} />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <footer className="bg-white border-t p-2 text-sm text-gray-600 flex justify-between items-center">
        <div>
          {processing ? '🔄 处理中...' : '🟢 就绪'}
        </div>
        <div className="flex items-center gap-2">
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-6 text-xs"
            onClick={saveCurrentConfig}
          >
            💾 保存配置
          </Button>
        </div>
      </footer>
    </div>
  );
};

export default EmailVerifier; 