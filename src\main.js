const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// 配置文件路径
const configPath = path.join(app.getPath('userData'), 'config.json');

// 创建主窗口
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 720,
    minWidth: 1000,
    minHeight: 680,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: true
    },
    autoHideMenuBar: true,
    frame: false,
    title: '邮箱验证码工具 v2.3'
  });

  // 在开发环境中，加载Vite开发服务器
  if (process.env.NODE_ENV !== 'production') {
    // 开发环境默认使用3000端口，如果被占用则尝试3001、3002等
    let port = 3000;
    const tryConnect = () => {
      mainWindow.loadURL(`http://localhost:${port}`)
        .catch(() => {
          port++;
          if (port < 3010) { // 最多尝试到3009端口
            tryConnect();
          } else {
            console.error('无法连接到开发服务器');
          }
        });
    };
    tryConnect();
    mainWindow.webContents.openDevTools();
  } else {
    // 在生产环境中，加载构建后的文件
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }
}

// 当Electron完成初始化时创建窗口
app.whenReady().then(() => {
  createWindow();

  // 设置IPC处理函数
  setupIPC();

  app.on('activate', function () {
    // 在macOS上，当点击dock图标且没有其他窗口打开时，
    // 通常会在应用程序中重新创建一个窗口
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// 设置IPC处理函数
function setupIPC() {
  // 保存配置
  ipcMain.handle('save-config', async (event, config) => {
    try {
      fs.writeFileSync(configPath, JSON.stringify(config), 'utf8');
      return { success: true };
    } catch (error) {
      console.error('保存配置失败:', error);
      return { success: false, error: error.message };
    }
  });

  // 加载配置
  ipcMain.handle('load-config', async (event) => {
    try {
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        return JSON.parse(configData);
      }
      return {};
    } catch (error) {
      console.error('加载配置失败:', error);
      return {};
    }
  });
} 